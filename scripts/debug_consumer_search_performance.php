<?php

/**
 * Consumer Search Performance Debug Script
 *
 * This script analyzes the performance issues with the consumer search endpoint
 * and provides recommendations for optimization.
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Builders\Odin\ConsumerSearchBuilder;
use App\Services\Filterables\Consumer\ConsumerFilterableService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ConsumerSearchPerformanceAnalyzer
{
    private array $testPayload;
    private ConsumerFilterableService $filterService;

    public function __construct()
    {
        // The problematic payload from the user
        $this->testPayload = [
            "search_id" => null,
            "search_text" => "",
            "filters" => [
                "consumer-date-range" => [
                    "from" => "2025-06-01T04:14:41.648100Z",
                    "to" => "2025-07-01T04:14:41.648110Z",
                    "preset" => "Last Month"
                ],
                "consumer-location" => [
                    "consumer-location" => [],
                    "consumer-county" => [],
                    "consumer-zip-code" => []
                ],
                "consumer-good-to-sell" => true,
                "consumer-industry" => [14]
            ],
            "page" => 1,
            "per_page" => 25,
            "company_id" => 66467,
            "campaign_id" => [],
            "sold" => null,
            "sort" => ["date" => null]
        ];

        $this->filterService = new ConsumerFilterableService();
    }

    public function analyzeQuery(): void
    {
        echo "=== Consumer Search Performance Analysis ===\n\n";

        // Step 1: Build the base query
        echo "1. Building base query...\n";
        $baseQuery = $this->buildBaseQuery();

        // Step 2: Apply filters
        echo "2. Applying filters...\n";
        $filteredQuery = $this->applyFilters($baseQuery);

        // Step 3: Analyze the generated SQL
        echo "3. Analyzing generated SQL...\n";
        $this->analyzeSql($filteredQuery);

        // Step 4: Check indexes
        echo "4. Checking database indexes...\n";
        $this->checkIndexes();

        // Step 5: Provide recommendations
        echo "5. Performance recommendations...\n";
        $this->provideRecommendations();
    }

    private function buildBaseQuery()
    {
        $builder = ConsumerSearchBuilder::query()
            ->searchId($this->testPayload['search_id'])
            ->searchText($this->testPayload['search_text'])
            ->companyId($this->testPayload['company_id'])
            ->campaignIds($this->testPayload['campaign_id'])
            ->soldToCompany($this->testPayload['sold']);

        return $builder->getQuery();
    }

    private function applyFilters($query)
    {
        $filterOptions = $this->testPayload['filters'];
        return $this->filterService->runQuery($filterOptions, $query);
    }

    private function analyzeSql($query): void
    {
        $sql = $query->toSql();
        $bindings = $query->getBindings();

        echo "Generated SQL:\n";
        echo str_repeat("-", 80) . "\n";
        echo $sql . "\n";
        echo str_repeat("-", 80) . "\n";
        echo "Bindings: " . json_encode($bindings) . "\n\n";

        // Count the number of joins
        $joinCount = substr_count(strtolower($sql), 'join');
        echo "Number of JOINs: $joinCount\n";

        // Check for subqueries
        $subqueryCount = substr_count(strtolower($sql), 'exists');
        echo "Number of EXISTS subqueries: $subqueryCount\n";

        // Check for LIKE operations
        $likeCount = substr_count(strtolower($sql), 'like');
        echo "Number of LIKE operations: $likeCount\n\n";
    }

    private function checkIndexes(): void
    {
        $tables = [
            'consumers',
            'consumer_products',
            'addresses',
            'product_assignments',
            'industry_services',
            'service_products'
        ];

        foreach ($tables as $table) {
            echo "Indexes for table '$table':\n";
            try {
                $indexes = DB::select("SHOW INDEX FROM $table");
                foreach ($indexes as $index) {
                    echo "  - {$index->Key_name} ({$index->Column_name})\n";
                }
            } catch (Exception $e) {
                echo "  Error: " . $e->getMessage() . "\n";
            }
            echo "\n";
        }
    }

    private function provideRecommendations(): void
    {
        echo "=== PERFORMANCE RECOMMENDATIONS ===\n\n";

        echo "1. MISSING INDEXES:\n";
        echo "   - Add composite index: consumer_products(good_to_sell, created_at, service_product_id)\n";
        echo "   - Add index: industry_services(industry_id) if missing\n";
        echo "   - Add composite index: consumer_products(consumer_id, good_to_sell, created_at)\n\n";

        echo "2. QUERY OPTIMIZATIONS:\n";
        echo "   - Consider caching results for expensive filter combinations\n";
        echo "   - Optimize EXISTS subqueries by converting to JOINs where possible\n";
        echo "   - Add query timeout handling\n\n";

        echo "3. CODE IMPROVEMENTS:\n";
        echo "   - Implement QueryCachingService for this endpoint\n";
        echo "   - Add database query monitoring\n";
        echo "   - Consider pagination optimization\n\n";

        echo "4. IMMEDIATE FIXES:\n";
        echo "   - Run the index creation migrations\n";
        echo "   - Add query timeout to prevent 502 errors\n";
        echo "   - Implement circuit breaker pattern\n\n";
    }
}

// Run the analysis if this script is executed directly
if (php_sapi_name() === 'cli') {
    $analyzer = new ConsumerSearchPerformanceAnalyzer();
    $analyzer->analyzeQuery();
}
