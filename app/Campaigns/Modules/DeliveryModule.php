<?php

namespace App\Campaigns\Modules;

use App\Actions\ForceUploadWatchdogVideo;
use App\Campaigns\Delivery\Contacts\ContactDeliverer;
use App\Campaigns\Delivery\CRM\BaseInteractableCRMDeliverer;
use App\Campaigns\Delivery\CRM\CRMDeliverer;
use App\Campaigns\Delivery\CRM\Enums\CRMType;
use App\Campaigns\Delivery\DeliveryType;
use App\Campaigns\Modules\DataModels\AllocateData;
use App\Campaigns\Modules\DataModels\PostAllocationData;
use App\Contracts\Campaigns\Delivery\DelivererContract;
use App\Contracts\Campaigns\HasFrontendComponent;
use App\DataModels\Campaigns\ConsumerProject;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\DTO\CompanyCampaignData\CompanyCampaignLeadAggregatesDTO;
use App\Http\Resources\Dashboard\v4\CompanyCampaignContactDeliveryResource;
use App\Http\Resources\Dashboard\v4\CompanyCampaignCRMDelivererResource;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModule;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleContact;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\Odin\ProductAssignment;
use App\Repositories\Campaigns\CompanyCampaignDataRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon as SupportCarbon;
use Illuminate\Support\Collection;

class DeliveryModule extends BaseModule implements HasFrontendComponent
{
    const PAYLOAD_PARENT_KEY           = 'delivery';
    const PAYLOAD_CONTACT_DELIVERIES   = 'contact_deliveries';
    const PAYLOAD_CRM_DELIVERIES       = 'crm_deliveries';
    const PAYLOAD_CONTACT_ID           = 'contact_id';
    const PAYLOAD_CONTACT_ACTIVE       = 'active';
    const PAYLOAD_CONTACT_SMS_ACTIVE   = 'sms_active';
    const PAYLOAD_CONTACT_EMAIL_ACTIVE = 'email_active';

    const PAYLOAD_CRM_TYPE         = 'crm_type';
    const PAYLOAD_CRM_ACTIVE       = self::PAYLOAD_CONTACT_ACTIVE;
    const PAYLOAD_CRM_DISPLAY_NAME = 'display_name';
    const PAYLOAD_CRM_PAYLOAD      = 'payload';
    const PAYLOAD_CRM_ID           = 'id';
    const PAYLOAD_CRM_TEMPLATE_ID  = 'template_id';

    const MODULE_FRONTEND_NAME = 'Delivery';

    public function __construct(
        protected CompanyCampaignDataRepository $campaignDataRepository,
        protected ForceUploadWatchdogVideo $forceUploadWatchdogVideo
    )
    {
    }

    /**
     * @inheritDoc
     */
    protected function getModel(CompanyCampaign $campaign): Model
    {
        /** @var CompanyCampaignDeliveryModule $model */
        $model = CompanyCampaignDeliveryModule::query()->firstOrCreate(
            [
                CompanyCampaignDeliveryModule::FIELD_CAMPAIGN_ID => $campaign->id,
            ],
            [
                CompanyCampaignDeliveryModule::FIELD_CAMPAIGN_ID => $campaign->id
            ]
        );

        return $model;
    }

    /**
     * @inheritDoc
     */
    public function validate(CompanyCampaign $campaign, Collection $payload): bool
    {
        // TODO: Implement validate() method.
        return true;
    }

    /**
     * @inheritDoc
     */
    public function transform(CompanyCampaign $campaign): Collection
    {
        /** @var CompanyCampaignDeliveryModule $deliveryModule */
        $deliveryModule = $this->getModel($campaign);

        return collect([
            self::PAYLOAD_PARENT_KEY => collect([
                self::PAYLOAD_CONTACT_DELIVERIES => CompanyCampaignContactDeliveryResource::collection($deliveryModule->contacts),
                self::PAYLOAD_CRM_DELIVERIES     => CompanyCampaignCRMDelivererResource::collection($deliveryModule->crms)
            ])
        ]);
    }

    /**
     * @inheritDoc
     */
    public function filter(CompanyCampaign $campaign, ConsumerProject $project): bool
    {
        return true;
    }

    /**
     * @inheritDoc
     */
    public function save(CompanyCampaign $campaign, Collection $payload): bool
    {
        $deliveryPayload = $payload[self::PAYLOAD_PARENT_KEY] ?? [];

        if (!$deliveryPayload) return true;

        return $this->syncContacts(
                $campaign,
                collect($deliveryPayload[self::PAYLOAD_CONTACT_DELIVERIES])
            ) && $this->syncCRMs(
                $campaign,
                collect($deliveryPayload[self::PAYLOAD_CRM_DELIVERIES])
            );
    }

    /**
     * Handles saving the contact delivery methods for this campaign.
     *
     * @param CompanyCampaign $campaign
     * @param Collection $payload
     * @return bool
     */
    protected function saveContacts(CompanyCampaign $campaign, Collection $payload): bool
    {
        $result = true;

        foreach ($payload as $contact) {
            if (!$result)
                break;

            $contact[self::PAYLOAD_CONTACT_ACTIVE] = ($contact[self::PAYLOAD_CONTACT_SMS_ACTIVE] || $contact[self::PAYLOAD_CONTACT_EMAIL_ACTIVE]);

            $result = (new ContactDeliverer(
                $contact[self::PAYLOAD_CONTACT_ID],
                $contact[self::PAYLOAD_CONTACT_ACTIVE],
                $contact[self::PAYLOAD_CONTACT_EMAIL_ACTIVE],
                $contact[self::PAYLOAD_CONTACT_SMS_ACTIVE],
            ))->save($campaign);
        }

        return $result;
    }

    /**
     * Handles saving the CRM delivery methods for this campaign.
     *
     * @param CompanyCampaign $campaign
     * @param Collection $payload
     * @return bool
     */
    protected function saveCRMs(CompanyCampaign $campaign, Collection $payload): bool
    {
        $interactableIds = [CRMType::PIPEDRIVE->value];
        $result          = true;

        foreach ($payload as $crm) {
            // Do not overwrite Pipedrive CRM payloads if the fields were never fetched, only update status/name changes
            if (in_array($crm[self::PAYLOAD_CRM_TYPE], $interactableIds)) {
                if (!array_key_exists(BaseInteractableCRMDeliverer::INTERACTABLE_FIELDS_KEY, $crm[self::PAYLOAD_CRM_PAYLOAD]) || !$crm[self::PAYLOAD_CRM_PAYLOAD][BaseInteractableCRMDeliverer::INTERACTABLE_FIELDS_KEY]) {
                    $result = (new CRMDeliverer(
                        CRMType::from($crm[self::PAYLOAD_CRM_TYPE]),
                        $crm[self::PAYLOAD_CRM_ACTIVE],
                        $crm[self::PAYLOAD_CRM_DISPLAY_NAME],
                        $crm[self::PAYLOAD_CRM_PAYLOAD],
                        intval($crm[self::PAYLOAD_CRM_ID]) ?: null,
                        $crm[self::PAYLOAD_CRM_TEMPLATE_ID] ?? null,
                    ))->updateBasicDetails($campaign);

                    continue;
                }
            }
            if (!$result)
                break;

            $result = (new CRMDeliverer(
                CRMType::from($crm[self::PAYLOAD_CRM_TYPE]),
                $crm[self::PAYLOAD_CRM_ACTIVE],
                $crm[self::PAYLOAD_CRM_DISPLAY_NAME],
                $crm[self::PAYLOAD_CRM_PAYLOAD],
                intval($crm[self::PAYLOAD_CRM_ID]) ?: null,
                $crm[self::PAYLOAD_CRM_TEMPLATE_ID] ?? null,
            ))->save($campaign);
        }

        return $result;
    }

    /**
     * @param CompanyCampaign $campaign
     * @param Collection $payload
     * @return bool
     */
    protected function syncContacts(CompanyCampaign $campaign, Collection $payload): bool
    {
        $contactIds = $payload->map(fn(array $contact) => $contact[CompanyCampaignDeliveryModuleContact::FIELD_CONTACT_ID])
            ->toArray();

        $campaign->deliveryModule?->contacts()
            ->whereNotIn(CompanyCampaignDeliveryModuleContact::FIELD_CONTACT_ID, $contactIds)
            ->delete();

        return $this->saveContacts($campaign, $payload);
    }

    /**
     * @param CompanyCampaign $campaign
     * @param Collection $payload
     * @return bool
     */
    protected function syncCRMs(CompanyCampaign $campaign, Collection $payload): bool
    {
        $crmIds = $payload->map(fn(array $crm) => $crm[CompanyCampaignDeliveryModuleCRM::FIELD_ID])
            ->toArray();

        $campaign->deliveryModule?->crms()
            ->whereNotIn(CompanyCampaignDeliveryModuleCRM::FIELD_ID, $crmIds)
            ->get()
            ->each(fn(CompanyCampaignDeliveryModuleCRM $crm) => $crm->delete());

        return $this->saveCRMs($campaign, $payload);
    }

    /**
     * @inheritDoc
     */
    public function getFrontendKey(): string
    {
        return self::PAYLOAD_PARENT_KEY;
    }

    /**
     * @inheritDoc
     */
    public function getFrontendModuleConfiguration(): ?array
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    public function getFrontendWizardConfiguration(): ?array
    {
        return [
            self::WIZARD_KEY_NAME       => self::MODULE_FRONTEND_NAME,
            self::WIZARD_KEY_MODULE_KEY => self::PAYLOAD_PARENT_KEY,
            self::WIZARD_KEY_INPUTS     => [
                self::PAYLOAD_CONTACT_DELIVERIES => [
                    self::INPUT_KEY_VALIDATION => 'array',
                    self::INPUT_KEY_NAME       => 'Contact Deliveries',
                    self::INPUT_CONTAINED_KEYS => [
                        self::PAYLOAD_CONTACT_ID           => [
                            self::INPUT_KEY_VALIDATION => 'numeric|min:1'
                        ],
                        self::PAYLOAD_CONTACT_ACTIVE       => [
                            self::INPUT_KEY_VALIDATION => 'boolean'
                        ],
                        self::PAYLOAD_CONTACT_SMS_ACTIVE   => [
                            self::INPUT_KEY_VALIDATION => 'boolean'
                        ],
                        self::PAYLOAD_CONTACT_EMAIL_ACTIVE => [
                            self::INPUT_KEY_VALIDATION => 'boolean'
                        ],
                    ],
                ],
                self::PAYLOAD_CRM_DELIVERIES     => [
                    self::INPUT_KEY_VALIDATION => 'array',
                    self::INPUT_KEY_NAME       => 'CRM Deliveries',
                    self::INPUT_CONTAINED_KEYS => [
                        self::PAYLOAD_CRM_TYPE         => [
                            self::INPUT_KEY_VALIDATION => '0'
                        ],
                        self::PAYLOAD_CRM_DISPLAY_NAME => [
                            self::INPUT_KEY_VALIDATION => 'string|min:3|max:64'
                        ],
                        self::PAYLOAD_CRM_ACTIVE       => [
                            self::INPUT_KEY_VALIDATION => 'boolean'
                        ],
                        self::PAYLOAD_CRM_PAYLOAD      => [
                            self::INPUT_KEY_VALIDATION => 'object'
                        ],
                    ],
                ],
            ],
        ];
    }

    /** @inheritDoc */
    public function allocate(CompanyCampaign $campaign, ProposedProductAssignment $proposedAssignment, AllocateData $data): AllocateData
    {
        return $data->setProductAssignments($data->getProductAssignments()->push($proposedAssignment->createProductAssignment()));
    }

    /** @inheritDoc */
    public function postAllocation(CompanyCampaign $campaign, ProductAssignment $assignment, PostAllocationData $data): PostAllocationData
    {

        $crmDeliveryMethods     = $this->getCrmDeliveryMethods($campaign)->filter(fn(CRMDeliverer $crm) => $crm->isActive());
        $contactDeliveryMethods = $this->getContactDeliveryMethods($campaign)->filter(fn(ContactDeliverer $contact) => $contact->isActive());

        if (!$crmDeliveryMethods->count() && !$contactDeliveryMethods->count())
            return $this->deliveryBounced($assignment, $data, DeliveryType::NONE, 'Campaign has no valid delivery methods');

        foreach ($assignment?->consumerProduct?->consumer?->watchdogVideos as $watchdogVideo) {
            $this->forceUploadWatchdogVideo->handle($watchdogVideo->watchdog_video_id);
        }

        $crmResult = $crmDeliveryMethods->map(
            fn(DelivererContract $deliverer) => $deliverer->deliver($assignment->consumerProduct, $campaign)
        )->filter()->isNotEmpty();

        if ($crmDeliveryMethods->count() > 0 && !$crmResult)
            $this->deliveryBounced($assignment, $data, DeliveryType::CRM, 'All CRM Deliveries Failed');

        $contactResult = $contactDeliveryMethods->map(
            fn(DelivererContract $deliverer) => $deliverer->deliver($assignment->consumerProduct, $campaign)
        )->filter()->isNotEmpty();

        if ($contactDeliveryMethods->count() && !$contactResult)
            $this->deliveryBounced($assignment, $data, DeliveryType::CONTACT, 'All Contact Deliveries Failed');

        if ($crmResult || $contactResult) {
            $this->deliverySucceeded($assignment, $data);

            $companyCampaignData = $this->campaignDataRepository->firstOrNew($campaign->id);

            $this->campaignDataRepository->saveData(
                campaignData            : $companyCampaignData,
                leadLastSoldAt          : now(),
            );
        }


        return $data;
    }

    /**
     * @param CompanyCampaign $campaign
     * @return Collection<int, DelivererContract>
     */
    private function getCrmDeliveryMethods(CompanyCampaign $campaign): Collection
    {
        /** @var CompanyCampaignDeliveryModule $model */
        $model = $this->getModel($campaign);
        return $model->crms->map(
            fn(CompanyCampaignDeliveryModuleCRM $crm) => new CRMDeliverer(
                $crm->crm_type,
                $crm->active,
                $crm->display_name,
                $crm->payload,
                $crm->id,
                $crm->template_id,
            )
        );
    }

    /**
     * @param CompanyCampaign $campaign
     * @return Collection<int, DelivererContract>
     */
    private function getContactDeliveryMethods(CompanyCampaign $campaign): Collection
    {
        /** @var CompanyCampaignDeliveryModule $model */
        $model = $this->getModel($campaign);
        return $model->contacts->map(
            fn(CompanyCampaignDeliveryModuleContact $contact) => new ContactDeliverer(
                contactId              : $contact->contact_id,
                active                 : $contact->active,
                emailDelivery          : $contact->email_active,
                smsDelivery            : $contact->sms_active,
                contactDeliveryModuleId: $contact->id
            )
        );
    }

    /**
     * @param ProductAssignment $assignment
     * @param PostAllocationData $data
     * @param DeliveryType $type
     * @param string $message
     * @return PostAllocationData
     */
    private function deliveryBounced(ProductAssignment $assignment, PostAllocationData $data, DeliveryType $type, string $message): PostAllocationData
    {
        return $data->logFailedAssignment($assignment, $type, $message);
    }

    /**
     * @param ProductAssignment $assignment
     * @param PostAllocationData $data
     * @return PostAllocationData
     */
    protected function deliverySucceeded(ProductAssignment $assignment, PostAllocationData $data): PostAllocationData
    {
        $assignment->update([
            ProductAssignment::FIELD_DELIVERED    => true,
            ProductAssignment::FIELD_DELIVERED_AT => Carbon::now()
        ]);
        return $data->logSuccessfulAssignment($assignment);
    }
}
