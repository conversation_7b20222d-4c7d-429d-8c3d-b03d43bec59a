<?php

namespace App\Enums\Billing;

use App\Services\Billing\ReviewableAction\ApplyCreditToCompanyApprovableAction;
use App\Services\Billing\ReviewableAction\MakeInvoicePaymentApprovableAction;
use App\Services\Billing\ReviewableAction\SaveBillingProfileApprovableAction;
use App\Services\Billing\ReviewableAction\ExpireCompanyCreditApprovableAction;
use App\Services\Billing\ReviewableAction\ExtendCompanyCreditApprovableAction;
use App\Services\Billing\ReviewableAction\InvoiceStatusUpdateApprovableAction;
use App\Services\Billing\ReviewableAction\IssueInvoiceRefundApprovableAction;
use App\Services\Billing\ReviewableAction\IssueInvoiceToCollections;
use App\Services\Billing\ReviewableAction\WriteOffInvoiceApprovableAction;
use Exception;
use Illuminate\Support\Str;

enum ApprovableActionType: string
{
    case UPDATE_INVOICE_STATUS        = 'update_invoice_status';
    case MAKE_INVOICE_PAYMENT         = 'make_invoice_payment';
    case APPLY_CREDIT_TO_COMPANY      = 'apply_credit_to_company';
    case ISSUE_INVOICE_TO_COLLECTIONS = 'issue_invoice_to_collections';
    case WRITE_OFF_INVOICE            = 'write_off_invoice';
    case ISSUE_INVOICE_REFUND         = 'issue_invoice_refund';
    case EXPIRE_COMPANY_CREDIT        = 'expire_company_credit';
    case EXTEND_COMPANY_CREDIT        = 'extend_company_credit';
    case UPDATE_BILLING_PROFILE       = 'update_billing_profile';
    case CREATE_BILLING_PROFILE       = 'create_billing_profile';

    /**
     * @return string
     * @throws Exception
     */
    public function getActionClass(): string
    {
        return match ($this) {
            self::UPDATE_INVOICE_STATUS                                => InvoiceStatusUpdateApprovableAction::class,
            self::APPLY_CREDIT_TO_COMPANY                              => ApplyCreditToCompanyApprovableAction::class,
            self::MAKE_INVOICE_PAYMENT                                 => MakeInvoicePaymentApprovableAction::class,
            self::ISSUE_INVOICE_TO_COLLECTIONS                         => IssueInvoiceToCollections::class,
            self::WRITE_OFF_INVOICE                                    => WriteOffInvoiceApprovableAction::class,
            self::ISSUE_INVOICE_REFUND                                 => IssueInvoiceRefundApprovableAction::class,
            self::EXPIRE_COMPANY_CREDIT                                => ExpireCompanyCreditApprovableAction::class,
            self::EXTEND_COMPANY_CREDIT                                => ExtendCompanyCreditApprovableAction::class,
            self::UPDATE_BILLING_PROFILE, self::CREATE_BILLING_PROFILE => SaveBillingProfileApprovableAction::class,
            default                                                    => throw new Exception('Action not supported. ' . $this->value)
        };
    }

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return match ($this) {
            self::UPDATE_INVOICE_STATUS        => 'Update Invoice Status',
            self::APPLY_CREDIT_TO_COMPANY      => 'Allocate Credit to Company',
            self::MAKE_INVOICE_PAYMENT         => 'Make Invoice Payment',
            self::ISSUE_INVOICE_TO_COLLECTIONS => 'Issue Invoice To Collections',
            self::WRITE_OFF_INVOICE            => 'Write Off Invoice',
            self::ISSUE_INVOICE_REFUND         => 'Issue Invoice Refund',
            self::EXPIRE_COMPANY_CREDIT        => 'Expire company credit',
            self::EXTEND_COMPANY_CREDIT        => 'Extend company credit',
            self::CREATE_BILLING_PROFILE       => 'Create Billing Profile',
            self::UPDATE_BILLING_PROFILE       => 'Update Billing Profile',
            default                            => Str::headline($this->value)
        };
    }

    public function getRelatedNotificationString(): string
    {
        return match ($this) {
            self::UPDATE_INVOICE_STATUS, self::MAKE_INVOICE_PAYMENT    => 'Invoice ID',
            self::APPLY_CREDIT_TO_COMPANY                              => 'Company ID',
            self::ISSUE_INVOICE_TO_COLLECTIONS                         => 'Issue Invoice To Collections',
            self::WRITE_OFF_INVOICE                                    => 'Write Off Invoice',
            self::ISSUE_INVOICE_REFUND                                 => 'Issue Invoice Refund',
            self::EXPIRE_COMPANY_CREDIT                                => 'Expire company credit',
            self::EXTEND_COMPANY_CREDIT                                => 'Extend company credit',
            self::CREATE_BILLING_PROFILE, self::UPDATE_BILLING_PROFILE => 'Billing Profile',
            default                                                    => $this->value
        };
    }
}
