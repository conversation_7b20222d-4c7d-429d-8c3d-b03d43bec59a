<?php

namespace App\Enums\MarketingCampaigns;

use App\Services\MarketingCampaign\Types\DripEmailBaseMarketingCampaign;
use App\Services\MarketingCampaign\Types\DripSMSBaseMarketingCampaign;
use App\Services\MarketingCampaign\Types\InternalEmailBaseMarketingCampaign;
use App\Services\MarketingCampaign\Types\MailchimpEmailBaseMarketingCampaign;
use App\Services\MarketingCampaign\Types\BaseMarketingCampaignType;
use App\Services\MarketingCampaign\Types\SMSBaseMarketingCampaign;

enum MarketingCampaignType: string
{
    case MAILCHIMP_EMAIL = 'mailchimp_email';
    case INTERNAL_EMAIL  = 'internal_email';
    case DRIP_EMAIL      = 'drip_email';
    case SMS             = 'sms';
    case DRIP_SMS        = 'drip_sms';

    public function sendable(): bool
    {
        return match ($this) {
            self::MAILCHIMP_EMAIL                                             => false,
            self::INTERNAL_EMAIL, self::DRIP_EMAIL, self::SMS, self::DRIP_SMS => true,
        };
    }

    public function drip(): bool
    {
        return match ($this) {
            self::DRIP_SMS, self::DRIP_EMAIL => true,
            default => false,
        };
    }

    public function internal(): bool
    {
        return match ($this) {
            self::MAILCHIMP_EMAIL => false,
            default => true,
        };
    }

    public function delaySend(): bool
    {
        return match ($this) {
            self::DRIP_EMAIL, self::DRIP_SMS, self::SMS => true,
            default => false,
        };
    }

    public function getClass(): BaseMarketingCampaignType
    {
        return match ($this) {
            self::MAILCHIMP_EMAIL => new MailchimpEmailBaseMarketingCampaign(),
            self::INTERNAL_EMAIL  => new InternalEmailBaseMarketingCampaign(),
            self::DRIP_EMAIL      => new DripEmailBaseMarketingCampaign(),
            self::SMS             => new SMSBaseMarketingCampaign(),
            self::DRIP_SMS        => new DripSMSBaseMarketingCampaign(),
        };
    }
}
