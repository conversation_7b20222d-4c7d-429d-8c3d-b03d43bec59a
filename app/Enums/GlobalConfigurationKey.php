<?php

namespace App\Enums;

enum GlobalConfigurationKey: string
{
    case SOLARREVIEWS                         = 'solarreviews_contacts';
    case FIXR                                 = 'fixr_contacts';
    case ROOFING_CALCULATOR                   = 'roofing_calculator';
    case TEST_PRODUCTS_EXPIRATION_GAP_IN_DAYS = 'test_products_expiration_gap_in_days';
    case GOOGLE_ADS_ACCOUNT_INDUSTRY_MAP      = 'google_ads_account_industry_map';
    case DEFAULT_PRICING                      = 'default-floor-pricing';
    case LEASING_COMPANY                      = 'leasing_company';
    case SLACK                                = 'slack';
    case FIXR_ACCOUNT                         = 'fixr_account';
    case SOLARREIVEWS_ACCOUNT                 = 'solarreviews_account';
    case MAILCHIMP                            = 'mailchimp';
    case MARKETING                            = 'marketing';
    case MARKETING_CALLBACK_PLACEHOLDERS      = 'marketing_callback_placeholder';
    case UPSELL_AUTOMATION_VARIABLES          = 'upsell_automation_variables';
    case PING_POST                            = 'ping_post';
    case MAILBOX_BLACKLIST                    = 'mailbox_blacklist';
    case PHONE_NUMBERS                        = 'phone_numbers';
    case AGED_QUEUE                           = 'aged_queue';
    case LEAD_REQUEST_AUTHORIZATION_TOKENS    = 'lead_request_authorization_tokens';
}
