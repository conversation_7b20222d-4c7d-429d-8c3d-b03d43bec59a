<?php

namespace App\Jobs;

use App\Enums\Calendar\CalendarEventStatus;
use App\Models\Calendar\CalendarEvent;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class CalculateMeetingsToSyncConferenceData implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const int CHUNK_SIZE = 100;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->queue = 'mailbox_handle_mail_provider_event';
    }

    /**
     *
     * @throws Exception
     */
    public function handle(): void
    {
        CalendarEvent::query()
            ->where(CalendarEvent::FIELD_START_TIME, '<=', now()->utc())
            ->where(CalendarEvent::FIELD_START_TIME, '>=', now()->utc()->subDays(4)->startOfDay())
            ->whereNotNull(CalendarEvent::FIELD_CONFERENCE_URL)
            ->where(CalendarEvent::FIELD_STATUS, CalendarEventStatus::CONFIRMED)
            ->whereNull(CalendarEvent::FIELD_LAST_CONFERENCE_DATA_SYNC_AT)
            ->chunk(self::CHUNK_SIZE, function (Collection $calendarEvents) {
                foreach ($calendarEvents as $calendarEvent) {
                    SyncConferenceData::dispatch($calendarEvent);
                }
            });
    }
}
