<?php

namespace App\Jobs;

use App\ConsumerProcessing\Jobs\AttemptConsumerProjectAllocationJob;
use App\ConsumerProcessing\Services\ConsumerProjectProcessingService;
use App\Jobs\Odin\UpdateLeadProcessingInitials;
use App\Jobs\Odin\UpdateLeadProcessingQueueConstraintsBucketFlags;
use App\Jobs\QAAutomation\QAAutomationApproveConsumerProductJob;
use App\Jobs\QAAutomation\QAAutomationApproveJob;
use App\Models\LeadProcessor;
use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\ConsumerProduct;
use App\Repositories\LeadProcessing\LeadProcessingQueueConstraintsRepository;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\Odin\DirectLeadsRepository;
use App\Services\ConsumerProductLifecycleTrackingService;
use App\Services\LeadProcessing\LeadProcessingService;
use App\Services\Odin\DuplicateProductService;
use App\Services\Odin\PingPostPublishing\PingPostPublishService;
use App\Services\OffHourLeadCompaniesService;
use App\Services\PubSub\PubSubService;
use App\Services\QAAutomation\QAAutomationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

/**
 * Class CreateInitialLead
 * @package App\Jobs
 */
class CreateInitialLead implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    private string $leadReference;

    public int $tries = 10;

    public int $maxExceptions = 1;

    /**
     * @param string $leadReference
     * @param int|null $consumerProductId
     */
    public function __construct(string $leadReference, protected ?int $consumerProductId = null)
    {
        $this->leadReference = $leadReference;
        $this->onQueue(config('queue.named_queues.appointment_allocation_queue'));
    }

    /**
     * @param LeadProcessingService $leadProcessingService
     * @param LeadProcessingQueueConstraintsRepository $leadProcessingQueueConstraintsRepository
     * @param PubSubService $pubSubService
     * @param DuplicateProductService $duplicateProductService
     * @param DirectLeadsRepository $directLeadsRepository
     * @param PingPostPublishService $pingPostPublishService
     * @param QAAutomationService $QAAutomationService
     * @throws BindingResolutionException
     */
    public function handle(
        LeadProcessingService $leadProcessingService,
        LeadProcessingQueueConstraintsRepository $leadProcessingQueueConstraintsRepository,
        PubSubService $pubSubService,
        DuplicateProductService $duplicateProductService,
        DirectLeadsRepository $directLeadsRepository,
        PingPostPublishService $pingPostPublishService,
        QAAutomationService $QAAutomationService,
    ): void
    {
        /** @var EloquentQuote $lead */
        $lead = EloquentQuote::where(EloquentQuote::REFERENCE, $this->leadReference)->first();

        if(!empty($lead)) {
            if($lead->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_INITIAL) {
                if($this->consumerProductId && $lead->multi_industry_lead && $duplicateProductService->cancelIfProductIsDuplicate($this->consumerProductId)) {
                    return;
                }

                // todo: address and re-enable once direct leads are to be activated
//                if ($this->consumerProductId && $directLeadsRepository->isConsumerProductEligibleForDirectLeads($this->consumerProductId) && config('sales.direct_leads.allocate_without_timezone_open_delay')) {
//                    $this->handleDirectLeads();
//                    return;
//                }

                // Check for buyers and industry, ping post publish lead if no available buyers
                if ($this->consumerProductId && $pingPostPublishService->checkPingPostValidity($this->consumerProductId)) {
                    PingPostPublishJob::dispatch($this->consumerProductId);
                } else if ($QAAutomationService->qualifyConsumerProduct($this->consumerProductId)) {
                    QAAutomationApproveConsumerProductJob::dispatch($this->consumerProductId);
                } else {
                    $leadProcessingQueueConstraintsRepository->saveLeadProcessingQueueConstraintsBucketFlags($lead, $this->consumerProductId);
                    $leadProcessingService->createInitialLead($lead->{EloquentQuote::QUOTE_ID}, $this->consumerProductId);
                }

                $pubSubService->handle("legacy-admin", "lead-created", ["last_lead_creation_timestamp" => now()->timestamp]);
            }
        }
        else {
            $this->release(60);
        }
    }

    /**
     * @return void
     */
    protected function handleDirectLeads(): void
    {
        //Allocate direct leads without timezone delay
        /** @var ConsumerProductRepository $repository */
        $repository = app(ConsumerProductRepository::class);

        /** @var ConsumerProjectProcessingService $service */
        $service = app(ConsumerProjectProcessingService::class);

        $consumerProduct = $repository->findOrFail($this->consumerProductId);

        $consumerProduct->update([ConsumerProduct::FIELD_STATUS => ConsumerProduct::STATUS_PENDING_ALLOCATION]);

        ConsumerProductLifecycleTrackingService::allocationAttemptScheduled($consumerProduct);
        AttemptConsumerProjectAllocationJob::dispatch(
            $service->prepareConsumerProject(
                consumer: $consumerProduct->consumer,
                address: $consumerProduct->address,
                lastQualifiedAt: now(),
                skipTimezoneDelay: true,
            )
        );
    }
}
