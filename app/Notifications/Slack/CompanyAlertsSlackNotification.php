<?php

namespace App\Notifications\Slack;

use App\Enums\CompanyConsolidatedStatus;
use App\Enums\GlobalConfigurationSlackNotificationField;
use App\Events\CompanyChangedPurchasingStatusEvent;
use App\Models\Odin\Company;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Slack\BlockKit\Blocks\SectionBlock;
use Illuminate\Notifications\Slack\SlackMessage;

class CompanyAlertsSlackNotification extends BaseSlackNotification
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        protected Company $company,
        protected string $changeTitle,
    )
    {}

    public function toSlack(object $notifiable): SlackMessage
    {
        return (new SlackMessage)
            ->sectionBlock(function (SectionBlock $block) {
                $block->field($this->company?->id . ": ". $this->company?->name . " " . $this->changeTitle);
            })
            ->sectionBlock(function (SectionBlock $block) {
                $block->field(config('app.url') .'/companies/'.$this->company?->id)->markdown();
            })
            ->sectionBlock(function (SectionBlock $block) {
                if($this->company?->businessDevelopmentManager?->name)
                    $block->field("*BDM:* ".$this->company?->businessDevelopmentManager?->name)->markdown();
                if($this->company?->accountManager?->name)
                    $block->field("*AM:* ".$this->company?->accountManager?->name)->markdown();
            })
            ->dividerBlock();
    }

    public function getSlackRoute(): ?StringableSlackRoute
    {
        $channelField = GlobalConfigurationSlackNotificationField::COMPANY_CHANEL;
        $tokenField = GlobalConfigurationSlackNotificationField::COMPANY_CHANEL_BOT_TOKEN;

        try {
            [$channel, $slackToken] = $this->getSlackRouteGlobalConfigVariables($channelField, $tokenField);
        } catch (Exception $e) {
            logger()->error($e->getMessage());
            return null;
        }

        return new StringableSlackRoute(
            $channel,
            $slackToken
        );
    }
}