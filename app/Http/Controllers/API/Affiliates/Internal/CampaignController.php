<?php

namespace App\Http\Controllers\API\Affiliates\Internal;

use App\Builders\Affiliates\AffiliateReportBuilder;
use App\Enums\Affiliate\ReportGrouping;
use App\Factories\JsonAPIResponseFactory;
use App\Helpers\CarbonHelper;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Affiliate\ListAffiliatesRequest;
use App\Http\Resources\Affiliates\CampaignReportResource;
use App\Models\Affiliates\Affiliate;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class CampaignController extends APIController
{
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param Affiliate $affiliate
     * @param ListAffiliatesRequest $request
     * @return array
     * @throws Exception
     */
    public function index(Affiliate $affiliate, ListAffiliatesRequest $request): array
    {
        $validated = $request->validated();

        $dateRange = Arr::get($validated, ListAffiliatesRequest::REQUEST_DATE_RANGE);

        $data = AffiliateReportBuilder::query()
            ->affiliateIds([$affiliate->id])
            ->fromDate(CarbonHelper::toTimezone(Arr::get($dateRange, ListAffiliatesRequest::REQUEST_FROM)))
            ->toDate(CarbonHelper::toTimezone(Arr::get($dateRange, ListAffiliatesRequest::REQUEST_TO)))
            ->name(Arr::get($validated, ListAffiliatesRequest::REQUEST_NAME))
            ->grouping(ReportGrouping::CAMPAIGN)
            ->sorting(Arr::get($validated, ListAffiliatesRequest::REQUEST_SORT_BY, []))
            ->getQuery();

        return CampaignReportResource::format($data);
    }
}
