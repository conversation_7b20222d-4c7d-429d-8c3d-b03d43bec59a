<?php

namespace App\Http\Controllers\API\Discovery;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Discovery\StoreCompanyProfileRequest;
use App\Http\Resources\CompanyProfile\CompanyProfileResource;
use App\Repositories\CompanyProfile\CompanyProfileRepository;
use App\Services\CompanyProfile\CompanyProfileSyncService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CompanyProfileAPIController extends APIController
{
    public function __construct(
        Request                             $request,
        JsonAPIResponseFactory              $apiResponseFactory,
        protected CompanyProfileSyncService $companyProfileSyncService,
        protected CompanyProfileRepository  $companyProfileRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    public function store(StoreCompanyProfileRequest $request): JsonResponse
    {
        $companyProfileDTO = $request->validated();

        $this->companyProfileSyncService->execute(profileDTO: $companyProfileDTO);

        //todo: Call FIXR.com service to create pages

        return response()->json();
    }

    public function show(string $profileSlug): JsonResponse
    {
        $profile = $this->companyProfileRepository->find(
            profileSlug: $profileSlug,
            published: true,
        );

        return $this->formatResponse([
            'status' => (bool)$profile,
            'profile' => $profile ? new CompanyProfileResource($profile) : null,
        ]);
    }

}
