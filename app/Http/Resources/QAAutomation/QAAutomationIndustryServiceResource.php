<?php

namespace App\Http\Resources\QAAutomation;

use App\Helpers\CarbonHelper;
use App\Models\Odin\IndustryService;
use App\Models\QAAutomation\QAAutomationIndustryService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class QAAutomationIndustryServiceResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        /** @var IndustryService $industryService */
        $industryService = $this->resource->{QAAutomationIndustryService::RELATION_INDUSTRY_SERVICE};

        return [
            'id' => $this->resource->{QAAutomationIndustryService::FIELD_ID},
            'industry_service_name' => $industryService->industry->name . ' - ' . $industryService->name,
            'industry_service_id' => $industryService->id,
            "type" => QAAutomationIndustryService::TYPE_STRINGS[$this->resource->{QAAutomationIndustryService::FIELD_TYPE}],
            "enabled" => $this->resource->{QAAutomationIndustryService::FIELD_ENABLED},
            "updated_at" => CarbonHelper::parse($this->resource->{QAAutomationIndustryService::FIELD_UPDATED_AT})->toFormat()
        ];
    }
}