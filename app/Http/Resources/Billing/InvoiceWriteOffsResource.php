<?php

namespace App\Http\Resources\Billing;

use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\InvoiceCollections;
use App\Models\Billing\InvoiceWriteOff;
use App\Models\User;
use Illuminate\Http\Client\Request;

/**
 * @mixin InvoiceWriteOff
 */
class InvoiceWriteOffsResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'recovery_status' => $this->{InvoiceCollections::FIELD_RECOVERY_STATUS},
            'invoice_id'      => $this->{InvoiceCollections::FIELD_INVOICE_ID},
            'invoice_total'   => $this->{InvoiceCollections::RELATION_INVOICE}?->getTotalIssuable(),
            'amount'          => $this->{InvoiceWriteOff::FIELD_AMOUNT},
            'user_id'         => $this->{InvoiceWriteOff::FIELD_USER_ID},
            'user_name'       => $this->{InvoiceWriteOff::RELATION_USER}?->{User::FIELD_NAME},
            'date'            => CarbonHelper::parseWithTimezone($this->{InvoiceWriteOff::FIELD_CREATED_AT})->toFormat()
        ];
    }
}
