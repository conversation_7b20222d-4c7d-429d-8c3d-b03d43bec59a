<?php

namespace App\Http\Resources\Billing\Reports;

use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\InvoiceCredit;
use App\Models\Odin\Company;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Number;

/**
 * @mixin InvoiceCredit
 */
class CreditMovementReportResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $company = $this->company_id ? Company::query()->findOrFail($this->company_id) : null;

        return [
            'invoice_id'             => $this->invoice_id,
            'amount'                 => Number::currency($this->value / 100),
            'applied_at'             => CarbonHelper::parseWithTimezone($this->created_at)->toFormat(),
            'company_id'             => $this->company_id,
            'company'                => [
                'id'   => $this->company_id,
                'name' => $company?->name,
            ],
            'credit_type'            => $this->credit_type,
            'action_type'            => $this->action_type,
            'last_lead_delivered_at' => $this->last_lead_delivered_at ? CarbonHelper::parseWithTimezone($this->last_lead_delivered_at)->toFormat() : null,
            'credit_expires_at'      => $this->credit_expires_at ? CarbonHelper::parseWithTimezone($this->credit_expires_at)->toFormat() : 'No expiry',
        ];
    }
}
