<?php

namespace App\Http\Resources\Billing;

use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\InvoiceCollections;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Number;

/**
 * @mixin InvoiceCollections
 */
class InvoiceCollectionsResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'sent_date'                   => CarbonHelper::parseWithTimezone($this->{InvoiceCollections::FIELD_SENT_DATE})->format('M d, Y h:i A'),
            'recovery_status'             => $this->{InvoiceCollections::FIELD_RECOVERY_STATUS},
            'invoice_id'                  => $this->{InvoiceCollections::FIELD_INVOICE_ID},
            'invoice_total'               => $this->{InvoiceCollections::RELATION_INVOICE}?->getTotalIssuable(),
            'invoice_total_in_dollars'    => ($this->{InvoiceCollections::RELATION_INVOICE}?->getTotalIssuable() ?? 0) / 100,
            'amount_recovered'            => $this->{InvoiceCollections::FIELD_AMOUNT_RECOVERED},
            'amount_recovered_in_dollars' => $this->{InvoiceCollections::FIELD_AMOUNT_RECOVERED} / 100,
            'amount_collected'            => $this->{InvoiceCollections::FIELD_AMOUNT_COLLECTED},
            'amount_collected_in_dollars' => $this->{InvoiceCollections::FIELD_AMOUNT_COLLECTED} / 100,
            'user_id'                     => $this->{InvoiceCollections::FIELD_USER_ID},
            'user_name'                   => $this->{InvoiceCollections::RELATION_USER}?->{User::FIELD_NAME},
            'recovery_date'               => $this->{InvoiceCollections::FIELD_RECOVERY_DATE} ? CarbonHelper::parseWithTimezone($this->{InvoiceCollections::FIELD_RECOVERY_DATE})->format('M d, Y h:i A') : null,
        ];
    }
}
