<?php

namespace App\Http\Resources\Billing;

use App\Http\Resources\Odin\BaseJsonResource;
use Illuminate\Http\Request;

/**
 * @mixin array
 */
class CompanyInvoiceSummaryResource extends BaseJsonResource
{
    const string TOTAL                      = 'total';
    const string PAID                       = 'paid';
    const string UNPAID                     = 'unpaid';
    const string OUTSTANDING                = 'outstanding_leads';
    const string NON_REJECTABLE_OUTSTANDING = 'non_rejectable_outstanding_leads';

    public function toArray(Request $request): array
    {
        return [
            self::TOTAL                      => (int)$this['total'],
            self::PAID                       => (int)$this['paid'],
            self::UNPAID                     => (int)$this['unpaid'],
            self::OUTSTANDING                => (int)$this['to_be_invoiced'],
            self::NON_REJECTABLE_OUTSTANDING => (int)$this['non_rejectable'],
        ];
    }
}