<?php

namespace App\Http\Resources\Affiliates;

use App\Helpers\CarbonHelper;
use App\Helpers\NumberHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Affiliates\Payout;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin ConsumerProduct
 */
class ConsumerProductResource extends BaseJsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        $payout = $this->{ConsumerProduct::RELATION_AFFILIATE_PAYOUT};
        $strategy = $payout->{Payout::RELATION_STRATEGY};

        return [
            "id" => $this->{ConsumerProduct::FIELD_ID},
            "created_at" => $this->created_at->timezone('MST')->format(CarbonHelper::FORMAT_BASE_TIMEZONE),
            "good_to_sell" => $this->{ConsumerProduct::FIELD_GOOD_TO_SELL},
            "status" => $this->getAffiliateStatus(),
            "consumer" => new ConsumerResource($this->{ConsumerProduct::RELATION_CONSUMER}),
            "address" => new AddressResource($this->{ConsumerProduct::RELATION_ADDRESS}),
            "service_product" => new ServiceProductResource($this->{ConsumerProduct::RELATION_SERVICE_PRODUCT}),
            "payout" => NumberHelper::currencyFromCents($payout->{Payout::FIELD_CENT_VALUE}),
            "legs_sold" => $this->{ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT}->count(),
            $this->mergeWhen($request->is('internal-api/*'), [
                "strategy" => new PayoutStrategyResource($strategy),
                "revenue" => NumberHelper::currency(
                    $this->{ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT}->sum(ProductAssignment::FIELD_COST)
                ),
            ]),
        ];
    }
}
