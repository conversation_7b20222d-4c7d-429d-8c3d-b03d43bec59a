<?php

namespace App\Http\Resources\Affiliates;

use App\Models\Affiliates\Campaign;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CampaignResource extends JsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            "id" => $this->{Campaign::FIELD_ID},
            "name" => $this->{Campaign::FIELD_NAME},
            "category_id" => $this->{Campaign::FIELD_CATEGORY_ID},
            "status" => $this->{Campaign::FIELD_STATUS}
        ];
    }
}
