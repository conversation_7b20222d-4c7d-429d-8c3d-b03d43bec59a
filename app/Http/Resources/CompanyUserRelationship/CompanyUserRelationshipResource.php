<?php

namespace App\Http\Resources\CompanyUserRelationship;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\CompanyUserRelationship;
use Illuminate\Http\Request;

/**
 * @mixin CompanyUserRelationship
 */
class CompanyUserRelationshipResource extends BaseJsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            'id'             => $this->id,
            'name'           => $this->user->name ?? 'Unknown',
            'commissionable' => $this->commissionable_at,
            'deleted_at'     => $this->deleted_at,
            'created_at'     => $this->created_at,
            'test'           => $this->commissionable_at?->timestamp,
            'active'         => !$this->deleted_at,
            'role'           => $this->role->name ?? 'Unknown',
        ];
    }
}
