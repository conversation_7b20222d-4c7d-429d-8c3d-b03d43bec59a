<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Class Location
 *
 * @property int $id
 * @property string $type
 * @property string $state_abbr
 * @property string $state
 * @property string $state_key
 * @property string $county
 * @property string $county_key
 * @property string $city
 * @property string $city_key
 * @property string $zip_code
 */
class Location extends LegacyModel
{
    use HasFactory;

    const TABLE = 'locations';

    const ID                 = 'id';
    const TYPE               = 'type';
    const ZIP_CODE           = 'zip_code';
    const STATE_ABBREVIATION = 'state_abbr';
    const STATE              = 'state';
    const STATE_KEY          = 'state_key';
    const COUNTY             = 'county';
    const COUNTY_KEY         = 'county_key';
    const CITY               = 'city';
    const CITY_KEY           = 'city_key';


    const TYPE_ZIP_CODE = 'zi';
    const TYPE_STATE    = 'st';
    const TYPE_COUNTY   = 'co';
    const TYPE_CITY     = 'ci';

    const TYPES = [
        self::TYPE_COUNTY,
        self::TYPE_ZIP_CODE,
        self::TYPE_STATE,
        self::TYPE_CITY
    ];

    /** @var string $table */
    protected $table      = self::TABLE;
    protected $primaryKey = self::ID;
    public    $timestamps = false;
}
