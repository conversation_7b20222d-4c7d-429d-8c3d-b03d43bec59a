<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use App\Models\Legacy\Location;
use App\Models\USZipCode;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property int $legacy_id
 * @property string $address_1
 * @property string $address_2
 * @property string $city
 * @property string|null $county
 * @property string $state
 * @property string $zip_code
 * @property string $country
 * @property float $latitude
 * @property float $longitude
 * @property string $place_id
 * @property int|null $utc
 * @property int|null $zip_code_location_id
 * @property int|null $county_location_id
 * @property int|null $state_location_id
 * @property string $full_address
 * @property boolean $imported
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Location $stateLocation
 * @property-read Collection<ConsumerProduct> $consumerProducts
 * @property-read USZipCode $usZipCode
 */
class Address extends BaseModel
{
    use HasFactory;

    const string TABLE = 'addresses';

    const string FIELD_ID                   = 'id';
    const string FIELD_ADDRESS_1            = 'address_1';
    const string FIELD_ADDRESS_2            = 'address_2';
    const string FIELD_CITY                 = 'city';
    const string FIELD_STATE                = 'state';
    const string FIELD_ZIP_CODE             = 'zip_code';
    const string FIELD_COUNTRY              = 'country';
    const string FIELD_LATITUDE             = 'latitude';
    const string FIELD_LONGITUDE            = 'longitude';
    const string FIELD_PLACE_ID             = 'place_id';
    const string FIELD_LEGACY_ID            = 'legacy_id';
    const string FIELD_UTC                  = 'utc';
    const string FIELD_IMPORTED             = 'imported';
    const string FIELD_COUNTY               = 'county';
    const string FIELD_ZIP_CODE_LOCATION_ID = 'zip_code_location_id';
    const string FIELD_COUNTY_LOCATION_ID   = 'county_location_id';
    const string FIELD_STATE_LOCATION_ID    = 'state_location_id';

    const RELATION_ZIPCODE                  = 'zipLocation';
    const string RELATION_STATE_LOCATION    = 'stateLocation';
    const string RELATION_CONSUMER_PRODUCTS = 'consumerProducts';
    const string RELATION_US_ZIP_CODE       = 'usZipCode';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return string
     */
    public function getObfuscatedAddress(): string
    {
        return $this->{self::FIELD_STATE} . ", " . $this->{self::FIELD_ZIP_CODE};
    }

    /**
     * @return string
     */
    public function getFullAddress(): string
    {
        return $this->getFullStreetAddress() . ", " . $this->{self::FIELD_CITY} . " " . $this->{self::FIELD_STATE} . ", " . $this->{self::FIELD_ZIP_CODE};
    }

    /**
     * Gets Concatenation of address1 and address2
     * @return string
     */
    public function getFullStreetAddress(): string
    {
        $addressString = $this->{self::FIELD_ADDRESS_1};
        if (!empty($this->{self::FIELD_ADDRESS_2})) {
            $addressString .= ", " . $this->{self::FIELD_ADDRESS_2};
        }
        return $addressString;
    }

    protected function fullAddress(): Attribute
    {
        return Attribute::make(
            get: fn ($value, $attributes) => collect([
                $attributes[self::FIELD_ADDRESS_1] . ($attributes[self::FIELD_ADDRESS_2] ? " {$attributes[self::FIELD_ADDRESS_2]}" : ''),
                $attributes[self::FIELD_CITY] . ' ' . $attributes[self::FIELD_STATE],
                $attributes[self::FIELD_ZIP_CODE]
            ])->join(', ')
        );
    }

    /**
     * @return string
     */
    static function getBadDataUuid(): string
    {
        return 'df8748c8-4b74-4f9a-8013-5cbf54590f12';
    }

    /**
     * @return string
     */
    public function getGoogleMapsQueryString(): string
    {
        return urlencode(str_replace(' ', '+', $this->getFullAddress()));
    }

    /**
     * @return HasOne
     */
    public function zipLocation(): HasOne
    {
        return $this->hasOne(Location::class, Location::ZIP_CODE, self::FIELD_ZIP_CODE)
            ->where(Location::TYPE, Location::TYPE_ZIP_CODE);
    }

    /**
     * @return HasOne
     */
    public function stateLocation(): HasOne
    {
        return $this->hasOne(Location::class, Location::ID, self::FIELD_STATE_LOCATION_ID);
    }

    /**
     * @return HasMany
     */
    public function consumerProducts(): HasMany
    {
        return $this->hasMany(ConsumerProduct::class, ConsumerProduct::FIELD_ADDRESS_ID, self::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function usZipCode(): HasOne
    {
        return $this->hasOne(USZipCode::class, USZipCode::FIELD_ZIP_CODE, self::FIELD_ZIP_CODE);
    }
}
