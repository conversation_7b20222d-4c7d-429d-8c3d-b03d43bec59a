<?php

namespace App\Models\Odin;

use App\Enums\Billing\BillingVersion;
use App\Events\ProductAssignmentSaved;
use App\Models\BaseModel;
use App\Models\Billing\InvoiceItem;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\LeadRefundItem;
use App\Models\Legacy\EloquentInvoice;
use App\Models\Legacy\EloquentInvoiceItem;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\SaleType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * @property int $id
 * @property int $legacy_id
 * @property int $company_id
 * @property int $consumer_product_id
 * @property float $cost
 * @property boolean $chargeable
 * @property boolean $delivered
 * @property int $exclude_budget
 * @property int $sale_type_id
 * @property int $campaign_id
 * @property int $product_campaign_budget_id
 * @property Carbon $delivered_at
 * @property Carbon $rejection_expiry
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property array|null $payload
 * @property boolean $offHourSale
 * @property int|null $parent_product_id // This is the id of the product we want to associate rejection value with
 * @property int $affect_rejection_percentage
 * @property boolean $conversion_uploaded
 * @property int $budget_id
 * @property int|null $quality_tier_id
 * @property ?int $company_opt_in_id
 *
 * @property-read Company $company
 * @property-read ConsumerProduct $consumerProduct
 * @property-read Consumer $consumer
 * @property-read Product $product
 * @property-read Collection|ProductRejection[] $productRejections
 * @property-read ProductCampaign $campaign
 * @property-read EloquentInvoiceItem $invoiceItem
 * @property-read LeadCampaignSalesTypeConfiguration $leadCampaignSalesTypeConfiguration
 * @property-read SaleType $saleType
 * @property-read Collection|ProductCancellation[] $productCancellations
 * @property-read ProductAppointment $appointment
 * @property-read Budget $budget
 * @property-read QualityTier|null $qualityTier
 * @property-read SingleProductSale|null $singleProductSale
 * @property-read OptInCompany|null $optInCompany
 *
 * @method static Builder sold()
 */
class ProductAssignment extends BaseModel
{
    use HasFactory, LogsActivity;

    const string TABLE = 'product_assignments';

    const string FIELD_ID                          = 'id';
    const string FIELD_COMPANY_ID                  = 'company_id';
    const string FIELD_CONSUMER_PRODUCT_ID         = 'consumer_product_id';
    const string FIELD_COST                        = 'cost';
    const string FIELD_CHARGEABLE                  = 'chargeable';
    const string FIELD_DELIVERED                   = 'delivered';
    const string FIELD_EXCLUDE_BUDGET              = 'exclude_budget';
    const string FIELD_SALE_TYPE_ID                = 'sale_type_id';
    const string FIELD_CAMPAIGN_ID                 = 'campaign_id';
    const string FIELD_PRODUCT_CAMPAIGN_BUDGET_ID  = 'product_campaign_budget_id';
    const string FIELD_DELIVERED_AT                = 'delivered_at';
    const string FIELD_REJECTION_EXPIRY            = 'rejection_expiry';
    const string FIELD_LEGACY_ID                   = 'legacy_id';
    const string FIELD_PAYLOAD                     = 'payload';
    const string FIELD_OFF_HOUR_SALE               = 'off_hour_sale';
    const string FIELD_PARENT_PRODUCT_ID           = 'parent_product_id';
    const string FIELD_AFFECT_REJECTION_PERCENTAGE = 'affect_rejection_percentage';
    const string FIELD_CONVERSION_UPLOADED         = 'conversion_uploaded';
    const string FIELD_BUDGET_ID                   = 'budget_id';
    const string FIELD_QUALITY_TIER_ID             = 'quality_tier_id';
    const string FIELD_SOLD_WHILE_UNDER_REVIEW     = 'sold_while_under_review';
    const string FIELD_COMPANY_OPT_IN_ID           = 'company_opt_in_id';

    const string RELATION_COMPANY                                = 'company';
    const string RELATION_CONSUMER_PRODUCT                       = 'consumerProduct';
    const string RELATION_CONSUMER                               = 'consumer';
    const string RELATION_PRODUCT                                = 'product';
    const string RELATION_PRODUCT_REJECTIONS                     = 'productRejections';
    const string RELATION_PRODUCT_CANCELLATIONS                  = 'productCancellations';
    const string RELATION_CAMPAIGN                               = 'campaign';
    const string RELATION_INVOICE_ITEM                           = 'invoiceItem';
    const string RELATION_SALE_TYPE                              = 'saleType';
    const string RELATION_APPOINTMENT                            = 'appointment';
    const string RELATION_LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION = 'leadCampaignSalesTypeConfiguration';
    const string RELATION_BUDGET                                 = 'budget';
    const string RELATION_QUALITY_TIER                           = 'qualityTier';
    const string RELATION_SINGLE_PRODUCT_SALE                    = 'singleProductSale';
    const string RELATION_LEAD_REFUNDS                           = 'leadRefunds';
    const string RELATION_LATEST_LEAD_REFUND_ITEM                = 'latestLeadRefundItem';
    const string RELATION_ODIN_INVOICE_ITEM                      = 'odinInvoiceItem';
    const string RELATION_OPT_IN_COMPANY                         = 'optInCompany';

    const int EXCLUDE_BUDGET_SUPER_PREMIUM = 1;
    const int EXCLUDE_BUDGET_TRIAL         = 2;
    const int EXCLUDE_BUDGET_PROMO         = 3;

    const string PAYLOAD_KEY_OPPORTUNITY_NOTIFICATION_CONFIG_ID = 'opportunity_notification_configuration_id';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_DELIVERED_AT     => 'datetime',
        self::FIELD_REJECTION_EXPIRY => 'datetime',
        self::FIELD_PAYLOAD          => 'array',
        self::FIELD_CHARGEABLE       => 'boolean',
        self::FIELD_DELIVERED        => 'boolean'
    ];

    protected static array $recordEvents  = ['updated'];
    protected array $logAttributes = [self::FIELD_CHARGEABLE];

    protected $dispatchesEvents = [
        'saved' => ProductAssignmentSaved::class,
    ];

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function consumerProduct(): BelongsTo
    {
        return $this->belongsTo(ConsumerProduct::class, self::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function productRejections(): HasMany
    {
        return $this->hasMany(ProductRejection::class, ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function productCancellations(): HasMany
    {
        return $this->hasMany(ProductCancellation::class, ProductCancellation::FIELD_PRODUCT_ASSIGNMENT_ID, self::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function campaign(): HasOne
    {
        return $this->hasOne(ProductCampaign::class, ProductCampaign::FIELD_ID, self::FIELD_CAMPAIGN_ID);
    }

    /**
     * @return HasOne
     */
    public function singleProductSale(): HasOne
    {
        return $this->hasOne(SingleProductSale::class);
    }

    /**
     * @return HasOneThrough
     */
    public function consumer(): HasOneThrough
    {
        return $this->hasOneThrough(
            Consumer::class,
            ConsumerProduct::class,
            ConsumerProduct::FIELD_ID,
            Consumer::FIELD_ID,
            self::FIELD_CONSUMER_PRODUCT_ID,
            ConsumerProduct::FIELD_CONSUMER_ID
        );
    }

    /**
     * @return HasOneThrough
     */
    public function invoiceItem(): HasOneThrough
    {
        return $this->hasOneThrough(
            EloquentInvoiceItem::class,
            EloquentQuoteCompany::class,
            EloquentQuoteCompany::ID,
            EloquentInvoiceItem::ID,
            ProductAssignment::FIELD_LEGACY_ID,
            EloquentQuoteCompany::INVOICE_ITEM_ID,
        );
    }

    public function odinInvoiceItem(): MorphOne
    {
        return $this->morphOne(InvoiceItem::class, 'billable');
    }

    /**
     * @return HasOneThrough
     */
    public function leadCampaignSalesTypeConfiguration(): HasOneThrough
    {
        return $this->hasOneThrough(
            LeadCampaignSalesTypeConfiguration::class,
            EloquentQuoteCompany::class,
            EloquentQuoteCompany::ID,
            LeadCampaignSalesTypeConfiguration::ID,
            ProductAssignment::FIELD_LEGACY_ID,
            EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID,
        );
    }

    /**
     * @return HasOne
     */
    public function saleType(): HasOne
    {
        return $this->hasOne(
            SaleType::class,
            SaleType::FIELD_ID,
            self::FIELD_SALE_TYPE_ID
        );
    }

    /**
     * @return HasOne
     */
    public function appointment(): HasOne
    {
        return $this->hasOne(
            ProductAppointment::class,
            ProductAppointment::CONSUMER_PRODUCT_ID,
            self::FIELD_CONSUMER_PRODUCT_ID
        );
    }

    /**
     * @return HasOneThrough
     */
    public function product(): HasOneThrough
    {
        // TODO: This is incorrect and doesn't work at all.
        return $this->hasOneThrough(Product::class, Product::FIELD_ID, self::FIELD_CONSUMER_PRODUCT_ID);
    }

    // todo: consumer

    /**
     * @return BelongsTo
     */
    public function budget(): BelongsTo
    {
        return $this->belongsTo(Budget::class);
    }

    /**
     * @return BelongsTo
     */
    public function qualityTier(): BelongsTo
    {
        return $this->belongsTo(QualityTier::class);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                self::FIELD_CHARGEABLE,
                self::FIELD_SALE_TYPE_ID,
                self::FIELD_COST,
                self::FIELD_AFFECT_REJECTION_PERCENTAGE,
            ])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * @return HasMany
     */
    public function leadRefunds(): HasMany
    {
        return $this->hasMany(LeadRefundItem::class, LeadRefundItem::FIELD_PRODUCT_ASSIGNMENT_ID, self::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function latestLeadRefundItem(): HasOne
    {
        return $this->hasOne(LeadRefundItem::class, LeadRefundItem::FIELD_PRODUCT_ASSIGNMENT_ID, self::FIELD_ID)
            ->latestOfMany();
    }

    /**
     * @return HasOne
     */
    public function latestLeadRefund(): HasOne
    {
        return $this->hasOne(LeadRefundItem::class, LeadRefundItem::FIELD_PRODUCT_ASSIGNMENT_ID, self::FIELD_ID)
            ->latestOfMany();
    }

    /**
     * @return EloquentInvoice|null
     */
    public function getEloquentInvoice()
    {
        return $this->{self::RELATION_INVOICE_ITEM}
            ?->{EloquentInvoiceItem::RELATION_ELOQUENT_INVOICE};
    }

    /**
     * @return BillingVersion
     */
    public function getBillingVersion(): BillingVersion
    {
        if (!$this->{ProductAssignment::FIELD_LEGACY_ID}) {
            return BillingVersion::V2;
        }

        return BillingVersion::tryFrom(
            EloquentQuoteCompany::query()
                ->where(EloquentQuoteCompany::ID, $this->{ProductAssignment::FIELD_LEGACY_ID})
                ?->first()
                ?->{EloquentQuoteCompany::BILLING_VERSION}
        ) ?? BillingVersion::V2;
    }

    /**
     * @return BelongsTo
     */
    public function optInCompany(): BelongsTo
    {
        return $this->belongsTo(OptInCompany::class, self::FIELD_COMPANY_OPT_IN_ID, OptInCompany::FIELD_ID);
    }

    /**
     * @return string|null
     */
    public function consolidatedLeadRefundStatus(): ?string
    {
        $latestLeadRefundItem = $this->{ProductAssignment::RELATION_LATEST_LEAD_REFUND_ITEM};

        if (!$latestLeadRefundItem) {
            return null;
        }

        $refundItemRefund = $latestLeadRefundItem->{LeadRefundItem::RELATION_LATEST_REFUND_ITEM_REFUND};

        if ($refundItemRefund) {
            return $refundItemRefund->status->value;
        }

        return $latestLeadRefundItem->status->value;
    }

    public function scopeSold(Builder $query): Builder
    {
        return $query
            ->where(self::FIELD_DELIVERED, true)
            ->where(self::FIELD_CHARGEABLE, true)
            ->whereDoesntHave(self::RELATION_PRODUCT_REJECTIONS)
            ->whereDoesntHave(self::RELATION_PRODUCT_CANCELLATIONS);
    }
}
