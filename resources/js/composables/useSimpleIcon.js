export default function useSimpleIcon() {
    const icons = {
        CHE<PERSON>ON_RIGHT: 'chevron-right',
        CHEVRON_LEFT: 'chevron-left',
        CHEVRON_DOWN: 'chevron-down',
        CHEVRON_UP: 'chevron-up',
        ARROW_LEFT: 'arrow-left',
        ARROW_RIGHT: 'arrow-right',
        CURVED_ARROW_LEFT: 'curved-arrow-left',
        CURVED_ARROW_RIGHT: 'curved-arrow-right',
        ARROW_UP: 'arrow-up',
        ARROW_DOWN: 'arrow-down',
        ARROW_TENDING_UP: 'arrow-tending-up',
        ARROW_TENDING_DOWN: 'arrow-tending-down',

        CURVED_DOUBLE_ARROW_LEFT: 'curved-double-arrow-left',
        ARROW_UTURN_LEFT: 'arrow-uturn-left',
        ARROW_UTURN_RIGHT: 'arrow-uturn-right',
        ARROW_TOP_RIGHT_ON_SQUARE: 'arrow-top-right-on-square',

        PENCIL: 'pencil',
        PENCIL_SQUARE: 'pencil-square',
        BIN: 'bin',

        CHECK: 'check',
        CLOC<PERSON>: 'clock',
        X_MARK: 'x-mark',
        ARROWS_POINTING_OUT: 'arrows-pointing-out',
        ARROWS_POINTING_IN: 'arrows-pointing-in',

        STAR: 'star',
        STAR_FILL: 'star-fill',

        MINUS: 'minus',
        PLUS: 'plus',
        COG_SIX_TOOTH: 'cog-6-tooth',

        INBOX: 'inbox',
        PAPER_AIRPLANE: 'paper-airplane',
        ARCHIVE_BOX_ARROW_DOWN: 'archive-box-arrow-down',
        ARCHIVE_BOX_X_MARK: 'archive-box-x-mark',
        DOCUMENT: 'document',
        USER_CIRCLE: 'user-circle',
        USER_GROUP: 'user-group',
        USERS_SOLID: 'users-solid',
        USER_PLUS: 'user-plus',
        FLAG: 'flag',
        ARCHIVE_BOX: 'archive-box',
        TAG: 'tag',
        CALENDAR_DAYS: 'calendar-days',

        ENVELOPE_OPEN: 'envelope-open',
        ENVELOPE: 'envelope',

        BOLT: 'bolt',
        BOOKMARK: 'bookmark',
        BOOKMARK_FILL: 'bookmark-fill',

        DOCUMENT_TEXT: 'document-text',
        DOCUMENT_CHECK: 'document-check',

        ELLIPSIS_HORIZONTAL: "ellipsis-horizontal",

        TRASH: 'trash',

        MAGNIFYING_GLASS: 'magnifying-glass',

        LOCK_CLOSED: 'lock-closed',
        LOCK_OPEN: 'lock-open',
        EYE: 'eye',
        EYE_SLASH: 'eye-slash',
        PLANT: 'plant',
        MONEY_DOLLAR: 'money_dollar',
        HAND_THUMB_UP_SOLID: 'hand-thumb-up-solid',
        HAND_THUMB_DOWN_SOLID: 'hand-thumb-down-solid',
        HAND_RAISED: 'hand-raised',

        ARROW_PATH_ROUNDED_SQUARE: 'arrow-path-rounded-square',
        ARROW_PATH: 'arrow-path',

        CALENDAR: 'calendar',
        CHAT_BUBBLE_BOTTOM_CENTER_TEXT: 'chat-bubble-bottom-center-text',
        ARROW_TRENDING_UP: 'arrow-trending-up',

        PAUSE_CIRCLE: 'pause-circle',
        PLAY_CIRCLE: 'play-circle',
        INFORMATION_CIRCLE: 'information-circle',
        X_CIRCLE: 'x-circle',
        CHECK_CIRCLE: 'check-circle',
        CIRCLE: 'circle',
        EXCLAMATION_CIRCLE: 'exclamation-circle',
        PLUS_CIRCLE: 'plus-circle',
        MINUS_CIRCLE: 'minus-circle',
        ARROW_UP_CIRCLE: 'arrow-up-circle',

        CURRENCY_DOLLAR: 'currency-dollar',

        UPDATED_AT: 'updated-at',
        CREATED_AT: 'created-at',
        CHARGED_AT: 'charged-at',

        BANK: 'bank',
        CASH: 'cash',
        CREDIT_CARD: 'credit_card',
        REQUESTER: 'requester',
        REVIEWER: 'reviewer',

        DOWNLOAD: 'download',

        LINK: 'link',

        ARROW_UP_RIGHT_FROM_SQUARE: 'arrow-up-right-from-square',

        ARROW_RIGHT_END_ON_RECTANGLE: 'arrow-right-end-on-rectangle',
        ARROW_LEFT_END_ON_RECTANGLE: 'arrow-left-end-on-rectangle',

        RSS: 'rss',
        TABLE_CELLS: 'table-cells',
        SQUARES_2X2: 'squares-2x2',
        IDENTIFICATION: 'identification',
        MEGAPHONE: 'megaphone',
        ADMIN_OVERRIDE: 'admin-override',
        ADMIN_APPROVED: 'admin-approved',
        ADMIN_COLLECTIONS: 'admin-archived',
        ADMIN_ARCHIVED: 'admin-archived',
        ADMIN_LOCKED: 'admin-locked',
        ADMIN_PROFILE_ONLY: 'admin-profile-only',
        SYSTEM_ELIGIBLE: 'system-eligible',
        SYSTEM_SUSPENDED_PAYMENT: 'system-suspended-payment',
        SYSTEM_SUSPENDED_PAYMENT_METHOD: 'system-suspended-payment-method',
        CAMPAIGN_NO_CAMPAIGNS: 'campaign-no-campaigns',
        CAMPAIGN_CAMPAIGNS_ACTIVE: 'campaign-campaigns-active',
        CAMPAIGN_CAMPAIGNS_PAUSED: 'campaign-campaigns-paused',
        CAMPAIGN_CAMPAIGNS_OFF: 'campaign-campaigns-off',
        CAMPAIGN_CAMPAIGNS_OFF_NEVER_PURCHASED: 'campaign-campaigns-off-never-purchased',
    }

    const sizes = {
        XS: 'xs',
        SM: 'sm',
        MD: 'md',
        LG: 'lg',
        XL: 'xl',
    }

    const colors = {
        BLACK: 'black',
        RED: 'red',
        BLUE: 'blue',
        WHITE: 'white',
        YELLOW: 'yellow',
        GRAY: 'gray',
        ORANGE: 'orange',
        GREEN: 'green',
        CURRENT: 'current'
    }

    return {
        icons,
        sizes,
        colors
    }
}
