import {defineStore} from "pinia";
import SharedApiService from "../vue/components/Shared/services/api";
import {ref} from "vue";

export const PERMISSIONS = {
    PERMISSION_ADMIN: 'admin',
    PERMISSION_MANAGEMENT_VIEW: 'permission-management/view',
    PERMISSION_MANAGEMENT_EDIT: 'permission-management/edit',
    PERMISSION_COMPANY_EDIT: 'companies/create',
    PERMISSION_FLOW_MANAGEMENT_DELETE: 'flow-management/delete',
    PERMISSION_COMPANY_EXPORT_DATA: 'company/export-data',
    PERMISSION_COMPANY_LINK_VIEW: 'company-link/view',
    PERMISSION_COMPANY_LINK_CREATE: 'company-link/create',
    PERMISSION_COMPANY_LINK_DELETE: 'company-link/delete',
    PERMISSION_COMPANY_CONFIGURE: 'company/configure',
    PERMISSION_COMPANY_MERGE: 'company/merge',
    PERMISSION_EMAIL_TEMPLATE: 'email-templates',
    PERMISSION_EMAIL_TEMPLATE_DELETE: 'email-templates/delete',
    PERMISSION_CONTRACT_MANAGEMENT_VIEW: 'contract-management/view',
    PERMISSION_CONTRACT_MANAGEMENT_EDIT: 'contract-management/edit',
    PERMISSION_CONTRACT_MANAGEMENT_SEND: 'contract-management/send',
    PERMISSION_CONTRACT_MANAGEMENT_UPLOAD: 'contract-management/upload',
    PERMISSION_INDUSTRY_CONFIGURATION: 'industry-configuration',
    PERMISSION_CAMPAIGNS_CREATE: 'campaigns/create',
    PERMISSION_CAMPAIGNS_UPDATE: 'campaigns/update',
    PERMISSION_CAMPAIGNS_DELETE: 'campaigns/delete',
    PERMISSION_CAMPAIGN_ENABLE_DISABLE_BIDDING: 'campaigns/enable-disable-bidding',
    PERMISSION_CAMPAIGN__EXCLUDE_FROM_AD_AUTOMATION: 'campaigns/exclude-from-ad-automation',
    PERMISSION_CAMPAIGN_MANAGE_PERMISSION: 'campaigns/manage-filters',
    PERMISSION_COMPANIES_LEADS_UPDATE_CHARGEABLE_STATUS: 'companies/leads/update-chargeable-status',
    PERMISSION_MAILBOX_LIST_EMAILS: 'mailbox/list-emails',
    PERMISSION_MAILBOX_SEND_EMAILS: 'mailbox/send-emails',
    PRIVACY_MANAGEMENT_REQUEST_CREATE: 'privacy-management/request/create',
    PRIVACY_MANAGEMENT_REQUEST_SEARCH: 'privacy-management/request/search',
    PRIVACY_MANAGEMENT_REQUEST_REDACT: 'privacy-management/request/redact',
    PERMISSION_ACTIVITY_LOGS_CAMPAIGNS_VIEW: 'activity-logs/campaigns/view',
    PERMISSION_RELATIONSHIP_MANAGER_VIEW: 'relationship-manager/view',
    PERMISSION_RELATIONSHIP_MANAGER_EDIT: 'relationship-manager/edit',
    PERMISSION_RELATIONSHIP_MANAGER_CREATE: 'relationship-manager/create',
    PERMISSION_RELATIONSHIP_MANAGER_DELETE: 'relationship-manager/delete',
    PERMISSION_VIEW_LEAD_PII: 'view-lead-pii',
    PERMISSION_LEAD_ALLOCATION_AND_ADJUSTMENT: 'lead-allocation-and-adjustment',
    PERMISSION_COMPANY_BASIC_STATUS_EDIT: 'company/basic-status/edit',
    PERMISSION_COMPANY_ADMIN_STATUS_EDIT: 'company/admin-status/edit',
    PERMISSION_COMPANY_PRE_ASSIGN_AM: 'company/pre-assign-am',
    PERMISSION_CONSUMER_SEARCH_EXPORT: 'consumer-search/export',
    PERMISSION_BILLING_CREATE_INVOICE: 'billing/create-invoice',
    PERMISSION_BILLING_CREDITS_COMPANY_VIEW: 'billing/credits/company/view',
    PERMISSION_BILLING_CREDITS_COMPANY_APPLY: 'billing/credits/company/apply',
    PERMISSION_BILLING_CREDITS_COMPANY_EXTEND: 'billing/credits/company/extend',
    PERMISSION_BILLING_CREDITS_COMPANY_EXPIRE: 'billing/credits/company/expire',
    PERMISSION_BILLING_CREDITS_TYPES_VIEW: 'billing/credits-types/view',
    PERMISSION_BILLING_CREDITS_TYPES_CREATE: 'billing/credits-types/create',
    PERMISSION_BILLING_CREDITS_TYPES_UPDATE: 'billing/credits-types/update',
    PERMISSION_BILLING_REPORTS_VIEW: 'billing/reports/view',
    PERMISSION_BILLING_ACTION_APPROVALS_VIEW: 'billing/action_approvals/view',
    PERMISSION_BILLING_ACTION_APPROVALS_REQUEST: 'billing/action_approvals/request',
    PERMISSION_BILLING_ACTION_APPROVALS_REVIEW: 'billing/action_approvals/review',
    PERMISSION_MARKETING_CAMPAIGNS_CREATE: 'marketing-campaigns/create',
    PERMISSION_MARKETING_CAMPAIGNS_EDIT: 'marketing-campaigns/edit',
    PERMISSION_TEMPLATE_MANAGEMENT: 'template-management',
    PERMISSION_SMS_TEMPLATE_VIEW: 'sms-template/view',
    PERMISSION_SMS_TEMPLATE_EDIT: 'sms-template/edit',
    PERMISSION_AFFILIATES_SHADOW: 'affiliates/shadow',
    PERMISSION_AFFILIATES_CREATE: 'affiliates/create',
    PERMISSION_AFFILIATES_UPDATE: 'affiliates/update',
    PERMISSION_AFFILIATES_DELETE: 'affiliates/delete',
    PERMISSION_BUSINESS_DEVELOPMENT_MANAGEMENT: 'business-development-management',
    PERMISSION_MINIMUM_PRICE_MANAGEMENT_VIEW : 'minimum-price-management/view',
    PERMISSION_MINIMUM_PRICE_MANAGEMENT_EDIT : 'minimum-price-management/edit',
    PERMISSION_LEAD_PROCESSING: 'lead-processing',
    PERMISSION_LEAD_PROCESSING_MANAGEMENT: 'lead-processing-management',
    PERMISSION_IMPERSONATE_USERS: 'impersonate-users',
    PERMISSION_COMPANY_OVERVIEW_LEAD_CHANNEL_BREAKDOWN: 'company/overview/lead-channel-breakdown',
    AGED_LEAD_QUEUE_REFRESH_QUEUE: 'aged-lead-queue/refresh-queue',
    PERMISSION_CONSUMER_REVIEWS_MANAGE: 'consumer-reviews/manage',
    COMPANY_DELETE: 'company/delete',
    COMPANY_COMMISSION_VIEW: 'company/commission/view',
    COMPANY_USER_RELATIONSHIPS_EDIT: 'company-user-relationships/edit',
    FLOW_ENGINES_CONFIGURABLE_VARIABLES_VIEW: 'flow-engines/configurable-variables/view',
    QA_AUTOMATION_INDUSTRY_SERVICE_MANAGEMENT_VIEW: 'qa-automation/industry-service/view',
    QA_AUTOMATION_INDUSTRY_SERVICE_MANAGEMENT_EDIT: 'qa-automation/industry-service/edit',
    QA_AUTOMATION_INDUSTRY_SERVICE_MANAGEMENT_DELETE: 'qa-automation/industry-service/delete',
    QA_AUTOMATION_RULE_MANAGEMENT_VIEW: 'qa-automation/rules/view',
    QA_AUTOMATION_RULE_MANAGEMENT_EDIT: 'qa-automation/rules/edit',
    QA_AUTOMATION_RULE_MANAGEMENT_DELETE: 'qa-automation/rules/delete',
};

export const ROLES = {
    ADMIN: 'admin',
    SALES_MANAGER: 'sales-manager',
    LEAD_REFUNDS_VIEWER: 'lead-refunds-viewer',
    LEAD_REFUNDS_REQUESTER: 'lead-refunds-requester',
    LEAD_REFUNDS_REVIEWER: 'lead-refunds-reviewer',
    LEAD_PROCESSOR: 'lead-processor',
    FINANCE_OWNER: 'finance-owner',
    FINANCE_CONTROLLER: 'finance-controller',
    BUSINESS_DEVELOPMENT_MANAGER: 'business-development-manager',
    ONBOARDING_MANAGER: 'onboarding-manager',
    PROSPECTOR: 'prospector',
    SALES_MANAGER_ADMIN: 'sales-manager-admin',
    LEADS_AUDITOR: 'leads-auditor',
}

export const IDS = {
    USER: 'user',
    ACCOUNT_MANAGER: 'accountManager',
    SUCCESS_MANAGER: 'successManager',
    SALES_TEAM_MEMBERS: 'salesTeamMembers',
}

export const useRolesPermissions = defineStore('roles-permissions',
    () => {
        const api = SharedApiService.make();
        const userRoles = ref([]);
        const userPermissions = ref([]);
        const userIds = ref({});
        const initialized = ref(false);

        const initialize = () => {
            return api.getUserRolesAndPermissions().then(resp => {
                userRoles.value = resp.data.data.roles;
                userPermissions.value = resp.data.data.permissions;
                userIds.value = resp.data.data.ids;
                initialized.value = true;
            });
        }

        const hasRole = (role) => {
            return !!userRoles.value.find(userRole => role === userRole);
        };

        const hasAnyRole = (roles) => {
            return roles.some(role => hasRole(role));
        };

        const hasPermission = (permission) => {
            return !!userPermissions.value.find(userPermission => userPermission === permission);
        };

        const getId = (idType) => {
            return userIds.value[idType] ?? null;
        }

        return {
            initialize,
            hasRole,
            hasAnyRole,
            hasPermission,
            getId,
            initialized
        }
    });
