import axios from "axios";

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }


    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`,
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'billing/invoices/reports', 1);
    }

    getReceivableInvoicesReport(params) {
        return this.axios().get('/receivable-invoices', {
            params
        })
    }

    getRevenueReport(params) {
        return this.axios().get('/revenue', {
            params
        })
    }

    getInvoiceStatuses() {
        return this.axios().get('/invoice-statuses')
    }

    getAgedReport(params){
        return this.axios().get('/aged-invoices', {
            params
        })
    }

    getInvoicesBalanceReport(params){
        return this.axios().get('/invoices-balance', {
            params
        })
    }

    getCreditsMovementReport(params){
        return this.axios().get('/credits-movement', {
            params
        })
    }


    getCompaniesOverviewReport(params){
        return this.axios().get('/companies-overview', {
            params
        })
    }

    getCreditsOutstandingReport(params){
        return this.axios().get('/credits-outstanding', {
            params
        })
    }
}
