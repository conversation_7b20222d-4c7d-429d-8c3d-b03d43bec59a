<template>
    <modal :dark-mode="darkMode" @close="$emit('close')" no-buttons>
        <template v-slot:header>Manage Company Credits</template>
        <template v-slot:content>
            <div class="flex flex-col">
                <simple-alert
                    v-if="alertMessage !== null"
                    :content="alertMessage"
                    :dark-mode="darkMode"
                />
                <display-pending-actions class="mb-4" :related-id="companyId" related-type="company"/>
                <add-company-credit-card class="mb-4" @credit-requested="handleCreditRequested" :dark-mode="darkMode" :company="company"/>
                <manage-company-credits-list @credit-action="handleCreditRequested" :dark-mode="darkMode" :company-id="companyId"/>
            </div>
        </template>
    </modal>
</template>
<script>
import Modal from "../../components/Modal.vue";
import AddCompanyCreditCard from "./AddCompanyCreditCard.vue";
import ManageCompanyCreditsList from "./ManageCompanyCreditsList.vue";
import SimpleAlert from "../../components/SimpleAlert.vue";
import DisplayPendingActions from "../../components/DisplayPendingActions/DisplayPendingApprovals.vue";
import {usePendingApprovals} from "../../../../../stores/pending-approvals.store.js";
import {useToastNotificationStore} from "../../../../../stores/billing/tost-notification.store.js";
export default {
    name: "ManageCompanyCreditsModal",
    components: {DisplayPendingActions, SimpleAlert, ManageCompanyCreditsList, AddCompanyCreditCard, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            required: true
        }
    },
    emits: ['close'],
    inject: ['company'],
    data() {
        return {
            alertMessage: null,
            pendingApprovalsStore: usePendingApprovals(),
            toastNotification: useToastNotificationStore()
        }
    },
    methods: {
        handleCreditRequested() {
            this.pendingApprovalsStore.listPendingApprovals({relatedId: this.companyId, relatedType: 'company'})
            this.alertMessage = 'Action requested';
            this.toastNotification.notifySuccess(this.alertMessage)
            setTimeout(() => {
                this.alertMessage = null
            }, 4000)
        }
    }
}
</script>
