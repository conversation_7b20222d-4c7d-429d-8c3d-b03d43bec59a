<template>
    <div class="flex gap-2 items-center">
        <span> every </span>
        <custom-inline-input
            :dark-mode="darkMode"
            v-model="modelValue.day"
            type="number"
        />
        <span> days from the last billed at </span>
    </div>
</template>
<script>
import CustomInlineInput from "../CustomInlineInput.vue";

export default {
    name: "MonthlyFrequency",
    components: {CustomInlineInput},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Object,
            default: {
                day: 7
            }
        }
    },
}
</script>
