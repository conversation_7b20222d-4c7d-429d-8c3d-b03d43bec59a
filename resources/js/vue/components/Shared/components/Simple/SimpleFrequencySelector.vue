<template>
    <div class="flex items-center gap-2 text-black font-semibold whitespace-nowrap">
        <span>Issue invoice </span>
        <div>
            <dropdown
                v-model="selectedType"
                :options="simpleFrequencyHelper.frequencyOptions"
                :dark-mode="darkMode"
            />
        </div>
        <component v-if="selectedType" :is="simpleFrequencyHelper.getFrequencyData(selectedType).component" :dark-mode="darkMode" v-model="cronData"/>
    </div>
</template>
<script>
import CustomCheckbox from "../../SlideWizard/components/CustomCheckbox.vue";
import CustomInput from "../CustomInput.vue";
import Dropdown from "../Dropdown.vue";
import DaysFrequency from "./DaysFrequency.vue";
import CustomButton from "../CustomButton.vue";
import {useSimpleFrequencyHelper} from "../../../../../composables/useSimpleFrequencyHelper.js";
const simpleFrequencyHelper = useSimpleFrequencyHelper()
export default  {
    name: "SimpleFrequencySelector",
    components: {CustomButton, Dropdown, CustomInput, CustomCheckbox, DaysFrequency},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        type: {
            type: String,
            required: true,
        },
        data: {
            type: Object,
            default: {},
        },
    },
    emits: ['cron-updated'],
    data() {
        return {
            selectedType: simpleFrequencyHelper.frequencies.MONTHLY,
            cronData: {},
            simpleFrequencyHelper
        }
    },
    created() {
        this.selectedType = this.type ?? this.selectedType
        this.cronData = this.data ?? {month_day: 1}
    },
    watch: {
        selectedType(type){
            this.cronData = this.simpleFrequencyHelper.getFrequencyData(type).defaults
        },
        cronData: {
            handler() {
                this.$emit('cron-updated', this.selectedType, this.cronData)
            },
            deep: true,
        }
    }
}
</script>
