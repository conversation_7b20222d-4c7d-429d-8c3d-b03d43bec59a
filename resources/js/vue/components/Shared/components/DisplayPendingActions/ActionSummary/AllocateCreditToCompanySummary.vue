<template>
    <div class="flex gap-1 flex-wrap">
        <p>Allocate</p>
        <strong>{{ $filters.centsToFormattedDollars(approval?.payload?.arguments?.amount) }}</strong>
        <p>of</p>
        <badge class="h-fit">{{ approval?.payload?.arguments?.name }}</badge>
        <p>to</p>
        <entity-hyperlink
            type="company"
            :entity-id="approval.company?.id"
            :prefix="approval.company?.name"
        >
        </entity-hyperlink>
    </div>
</template>
<script>
import EntityHyperlink from "../../../../BillingManagement/components/EntityHyperlink.vue";
import Badge from "../../Badge.vue";

export default {
    name: "AllocateCreditToCompanySummary",
    components: {Badge, EntityHyperlink},
    props: {
        approval: {
            type: Object,
            default: {}
        }
    }
}
</script>
