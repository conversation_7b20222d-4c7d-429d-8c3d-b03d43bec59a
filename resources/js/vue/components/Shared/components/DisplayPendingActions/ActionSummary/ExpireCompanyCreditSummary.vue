<template>
    <div class="flex flex-col gap-1">
        <div class="flex gap-1 flex-wrap">
            <p>Expire the credit for</p>
            <entity-hyperlink
                type="company"
                :entity-id="approval?.company?.id"
                :prefix="approval?.company?.name"
            >
            </entity-hyperlink>
        </div>
        <div class="flex gap-2 flex-wrap">
            <labeled-value label="Credit Type">
                <badge>
                    {{ approval?.payload?.arguments?.creditType }}
                </badge>
            </labeled-value>
            <labeled-value label="Remaining value">
                <badge>
                    {{ $filters.centsToFormattedDollars(approval?.payload?.arguments?.creditRemainingValue) }}
                </badge>
            </labeled-value>
            <labeled-value label="Initial value">
                <badge>
                    {{ $filters.centsToFormattedDollars(approval?.payload?.arguments?.creditInitialValue) }}
                </badge>
            </labeled-value>

            <labeled-value label="Old expiry date">
                <badge>
                    {{ $filters.dateFromTimestamp(approval?.payload?.arguments?.oldExpiryDate) }}
                </badge>
            </labeled-value>

            <labeled-value label="New Expiry date">
                <badge>
                    {{ $filters.dateFromTimestamp(approval?.payload?.arguments?.newCreditExpiryDate) }}
                </badge>
            </labeled-value>
        </div>
    </div>
</template>
<script>
import EntityHyperlink from "../../../../BillingManagement/components/EntityHyperlink.vue";
import Badge from "../../Badge.vue";
import LabeledValue from "../../LabeledValue.vue";

export default {
    name: "ExpireCompanyCreditSummary",
    components: {LabeledValue, Badge, EntityHyperlink},
    props: {
        approval: {
            type: Object,
            default: {}
        }
    }
}
</script>
