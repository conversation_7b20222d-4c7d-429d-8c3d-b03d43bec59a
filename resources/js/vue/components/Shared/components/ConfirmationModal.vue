<template>
    <Modal
        small
        :dark-mode="darkMode"
        hide-confirm
        hide-cancel
        no-close-button
        no-min-height
    >
        <template #header>
            <slot name="header">
                <h2 class="text-xl font-medium" :class="titleClass">{{ title }}</h2>
            </slot>
        </template>
        <template #content>
            <slot name="content">
                <p class="text-lg">{{ text }}</p>
            </slot>
        </template>
        <template #buttons>
            <slot name="buttons">
                <CustomButton
                    @click="$emit('choice', false)"
                    class="order-last"
                    height="h-10"
                    :color="cancelButtonColor"
                >
                    {{ cancelButtonText }}
                </CustomButton>
                <CustomButton
                    @click="$emit('choice', true)"
                    class="order-last"
                    height="h-10"
                    :color="confirmButtonColor"
                >
                    {{ confirmButtonText }}
                </CustomButton>
            </slot>
        </template>
    </Modal>
</template>

<script>
import Modal from "./Modal.vue";
import CustomButton from "./CustomButton.vue";

export default {
    name: "ConfirmModal",
    components: {CustomButton, Modal},
    props: {
        title: {
            type: String,
            default: "Confirm Action"
        },
        text: {
            type: String,
            default: "Are you sure you want to proceed?"
        },
        titleClass: {
            type: String,
            default: "text-red-400"
        },
        cancelButtonText: {
            type: String,
            default: "Cancel"
        },
        confirmButtonText: {
            type: String,
            default: "Confirm"
        },
        cancelButtonColor: {
            type: String,
            default: "slate-light"
        },
        confirmButtonColor: {
            type: String,
            default: "primary"
        },
        darkMode: {
            type: Boolean,
            default: false
        }
    }
};
</script>
