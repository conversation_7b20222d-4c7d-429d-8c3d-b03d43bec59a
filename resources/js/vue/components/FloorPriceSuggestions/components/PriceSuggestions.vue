<template>
    <div class="border rounded-lg overflow-hidden relative min-h-96" :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <Tab
            :dark-mode="darkMode"
            :tabs="tabs"
            :tab-type="'Normal'"
            :tabs-classes="'w-full md:w-2/3 lg:w-1/3'"
        />

        <div class="grid grid-cols-6 gap-3 px-5 mt-6 pb-3">
            <div v-if="industryFilterOptions">
                <p :class="[darkMode ? 'text-slate-300' : 'text-slate-500']" class="mb-2 text-sm font-medium">Industry</p>
                <Dropdown :dark-mode="darkMode" v-model="industryId" :options="industryFilterOptions" :disabled="loadingAll" v-on:update:modelValue="handleIndustryChange"/>
            </div>
            <div v-if="industryServiceOptions">
                <p :class="[darkMode ? 'text-slate-300' : 'text-slate-500']" class="mb-2 text-sm font-medium">Industry Service</p>
                <Dropdown :dark-mode="darkMode" v-model="industryServiceId" :options="industryServiceOptions" :disabled="loadingAll" v-on:update:modelValue="handleServiceChange"/>
            </div>
            <div>
                <p :class="[darkMode ? 'text-slate-300' : 'text-slate-500']" class="mb-2 text-sm font-medium">Property Type</p>
                <Dropdown :dark-mode="darkMode" v-model="propertyType" :options="propertyTypeFilterOptions" :disabled="loadingAll" />
            </div>
            <div>
                <p :class="[darkMode ? 'text-slate-300' : 'text-slate-500']" class="mb-2 text-sm font-medium">Quality Tier</p>
                <Dropdown :dark-mode="darkMode" v-model="qualityTier" :options="qualityTierFilterOptions" :disabled="loadingAll" />
            </div>
        </div>
        <div class="p-5 border-t border-b my-5 flex gap-3 items-center" :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}">
            <p class="font-medium text-md mr-3">Price Margin:</p>
            <div>
                <p class="text-sm">Exclusive (%)</p>
                <custom-input :type="'number'" :dark-mode="darkMode" :disabled="loadingPriceMargin" v-model="priceMargin.exclusive_margin"></custom-input>
            </div>
            <div>
                <p class="text-sm">Duo (%)</p>
                <custom-input :type="'number'" :dark-mode="darkMode" :disabled="loadingPriceMargin" v-model="priceMargin.duo_margin"></custom-input>
            </div>
            <div>
                <p class="text-sm">Tio (%)</p>
                <custom-input :type="'number'" :dark-mode="darkMode" :disabled="loadingPriceMargin" v-model="priceMargin.trio_margin"></custom-input>
            </div>
            <div>
                <p class="text-sm">Quad (%)</p>
                <custom-input :type="'number'" :dark-mode="darkMode" :disabled="loadingPriceMargin" v-model="priceMargin.quad_margin"></custom-input>
            </div>
            <div class="self-end">
                <custom-button :dark-mode="darkMode" :disabled="loadingPriceMargin" @click="updatePriceMargin">Update Margin</custom-button>
            </div>
        </div>
        <div class="px-5 my-3" v-for="state in configuration.states" v-if="selectedState">
            <div class="flex gap-3 items-center justify-between w-full">
                <div class="w-full">
                    <div class="text-md grid grid-cols-2 gap-3 items-center w-full justify-between pb-3" :class="[stateLoadingClass]">
                        <div class="flex items-center gap-2">
                            <p class="font-semibold">{{ state.state }}</p>
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 font-bold"
                                 v-if="selectedState.state_key !== state.state_key" @click="setSelectedState(state)">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15"/>
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 font-bold" v-else
                                 @click="setSelectedState({})">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M5 12h14"/>
                            </svg>
                        </div>
                        <div class="flex items-center ml-auto" v-if="selectedState.state_key === state.state_key && selectedPrices.length">
                            <p class="text-cyan-500 text-md text-nowrap mr-1">Lowered Price Policy</p>
                            <div class="flex gap-1 items-center mr-5">
                                <Dropdown :dark-mode="darkMode" v-model="selectedLoweredFloorPolicy" :options="loweredFloorPolicyOptions" :disabled="savingPrices" />
                                <Tooltip
                                    :dark-mode="darkMode"
                                    :right="true"
                                >
                                    <template v-slot:default>
                                        <p class="whitespace-pre-wrap">
                                            {{ loweredFloorPolicyDescription[selectedLoweredFloorPolicy] ?? '' }}
                                        </p>
                                    </template>
                                </Tooltip>
                            </div>
                            <custom-button @click="applyPrices" :disabled="savingPrices">Apply Suggested Prices</custom-button>
                        </div>
                    </div>
                    <div v-if="selectedState.state_key === state.state_key" class="min-h-48 flex items-center justify-center w-full">
                        <loading-spinner v-if="loadingAll"></loading-spinner>
                        <div v-else class="w-full p-5 rounded-lg" :class="{'bg-light-background': !darkMode, 'bg-dark-background': darkMode}">
                            <div class="grid grid-cols-6 border-b mb-7 text-sm items-center pb-2" v-if="suggestedStatePrices"
                                 :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}">
                                <div>
                                    <p class="text-xl font-semibold">{{ suggestedStatePrices.location.state }}</p>
                                    <p>Active Campaigns: {{ loadingStatistics ? '....' : getActiveCampaignsByKey(suggestedStatePrices.location.state_key) }}</p>
                                    <p>Average legs sold: {{ loadingStatistics ? '....' : getLegsSoldByKey(suggestedStatePrices.location.state_key) }}</p>
                                </div>
                                <div class="flex gap-2 items-center">
                                    <div class="font-semibold">Exclusive</div>
                                    <div class="h-[3rem] border-2" :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}"></div>
                                    <div>
                                        <p class="mb-2">Current:
                                            <badge :color="'indigo'" class="ml-[1.25rem]">{{ getCurrentPrice(suggestedStatePrices.location, 'exclusive') }}</badge>
                                        </p>
                                        <p>Suggested:
                                            <badge :color="'cyan'">${{ suggestedStatePrices.suggested_price_exclusive }}</badge>
                                        </p>
                                    </div>
                                </div>
                                <div class="flex gap-2 items-center">
                                    <div class="font-semibold">Duo</div>
                                    <div class="h-[3rem] border-2" :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}"></div>
                                    <div>
                                        <p class="mb-2">Current:
                                            <badge :color="'indigo'" class="ml-[1.25rem]">{{ getCurrentPrice(suggestedStatePrices.location, 'duo') }}</badge>
                                        </p>
                                        <p>Suggested:
                                            <badge :color="'cyan'">${{ suggestedStatePrices.suggested_price_duo }}</badge>
                                        </p>
                                    </div>
                                </div>
                                <div class="flex gap-2 items-center">
                                    <div class="font-semibold">Trio</div>
                                    <div class="h-[3rem] border-2" :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}"></div>
                                    <div>
                                        <p class="mb-2">Current:
                                            <badge :color="'indigo'" class="ml-[1.25rem]">{{ getCurrentPrice(suggestedStatePrices.location, 'trio') }}</badge>
                                        </p>
                                        <p>Suggested:
                                            <badge :color="'cyan'">${{ suggestedStatePrices.suggested_price_trio }}</badge>
                                        </p>
                                    </div>
                                </div>
                                <div class="flex gap-2 items-center">
                                    <div class="font-semibold">Quad</div>
                                    <div class="h-[3rem] border-2" :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}"></div>
                                    <div>
                                        <p class="mb-2">Current:
                                            <badge :color="'indigo'" class="ml-[1.25rem]">{{ getCurrentPrice(suggestedStatePrices.location, 'quad') }}</badge>
                                        </p>
                                        <p>Suggested:
                                            <badge :color="'cyan'">${{ suggestedStatePrices.suggested_price_quad }}</badge>
                                        </p>
                                    </div>
                                </div>
                                <p>
                                    <custom-checkbox v-model="suggestedStatePrices.selected" :input-disabled="savingPrices"></custom-checkbox>
                                </p>
                            </div>
                            <p class="my-5 font-semibold text-xl text-center">Counties</p>
                            <div class="grid grid-cols-6 border-b my-5 text-sm items-center pb-2" v-if="suggestedCountyPrices" v-for="suggestedPrice in suggestedCountyPrices"
                                 :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}">
                                <div>
                                    <p class="text-xl font-semibold">{{ suggestedPrice.location.county }}</p>
                                    <p>Active Campaigns: {{ loadingStatistics ? '....' : getActiveCampaignsByKey(suggestedPrice.location.county_key) }}</p>
                                    <p>Average legs sold: {{ loadingStatistics ? '....' : getLegsSoldByKey(suggestedPrice.location.county_key) }}</p>
                                </div>
                                <div class="flex gap-2 items-center">
                                    <div class="font-semibold">Exclusive</div>
                                    <div class="h-[3rem] border-2" :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}"></div>
                                    <div>
                                        <p class="mb-2">Current:
                                            <badge :color="'indigo'" class="ml-[1.25rem]">{{ getCurrentPrice(suggestedPrice.location, 'exclusive') }}</badge>
                                        </p>
                                        <p>Suggested:
                                            <badge :color="'cyan'">${{ suggestedPrice.suggested_price_exclusive }}</badge>
                                        </p>
                                    </div>
                                </div>
                                <div class="flex gap-2 items-center">
                                    <div class="font-semibold">Duo</div>
                                    <div class="h-[3rem] border-2" :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}"></div>
                                    <div>
                                        <p class="mb-2">Current:
                                            <badge :color="'indigo'" class="ml-[1.25rem]">{{ getCurrentPrice(suggestedPrice.location, 'duo') }}</badge>
                                        </p>
                                        <p>Suggested:
                                            <badge :color="'cyan'">${{ suggestedPrice.suggested_price_duo }}</badge>
                                        </p>
                                    </div>
                                </div>
                                <div class="flex gap-2 items-center">
                                    <div class="font-semibold">Trio</div>
                                    <div class="h-[3rem] border-2" :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}"></div>
                                    <div>
                                        <p class="mb-2">Current:
                                            <badge :color="'indigo'" class="ml-[1.25rem]">{{ getCurrentPrice(suggestedPrice.location, 'trio') }}</badge>
                                        </p>
                                        <p>Suggested:
                                            <badge :color="'cyan'">${{ suggestedPrice.suggested_price_trio }}</badge>
                                        </p>
                                    </div>
                                </div>
                                <div class="flex gap-2 items-center">
                                    <div class="font-semibold">Quad</div>
                                    <div class="h-[3rem] border-2" :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}"></div>
                                    <div>
                                        <p class="mb-2">Current:
                                            <badge :color="'indigo'" class="ml-[1.25rem]">{{ getCurrentPrice(suggestedPrice.location, 'quad') }}</badge>
                                        </p>
                                        <p>Suggested:
                                            <badge :color="'cyan'">${{ suggestedPrice.suggested_price_quad }}</badge>
                                        </p>
                                    </div>
                                </div>
                                <p>
                                    <custom-checkbox v-model="suggestedPrice.selected" :input-disabled="savingPrices"></custom-checkbox>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <alerts-container :dark-mode="darkMode" :alert-type="alertType" v-if="alertActive" :text="alertText"></alerts-container>
    </div>
</template>

<script>
import {ApiService} from "../services/api.js";
import Dropdown from "../../Shared/components/Dropdown.vue";
import Tab from "../../Shared/components/Tab.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import CustomCheckbox from "../../Shared/SlideWizard/components/CustomCheckbox.vue";
import {ApiFactory} from "../../FloorPriceManagement/services/api/factory.js";
import Badge from "../../Shared/components/Badge.vue";
import Tooltip from "../../Shared/components/Tooltip.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
import AlertsMixin from "../../../mixins/alerts-mixin.js";

export default {
    name: "PriceSuggestions",
    components: {AlertsContainer, CustomInput, Tooltip, Badge, CustomCheckbox, LoadingSpinner, CustomButton, Tab, Dropdown},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        api: {
            type: ApiService,
            required: true
        },
        configuration: {
            type: Object,
            default: {}
        }
    },
    mixins: [AlertsMixin],
    data() {
        return {
            floorPriceApi: ApiFactory.makeApiService('api'),
            tabs: [{ name: 'Lead', current: true }],
            industryFilterOptions: [],
            industryServiceOptions: [],
            industryId: null,
            industryServiceId: null,
            propertyTypeFilterOptions: [],
            qualityTierFilterOptions: [],
            propertyType: 'Residential',
            qualityTier: 'Standard',
            selectedState: {},
            loading: false,
            suggestedPrices: [],
            stateFloorPrices: {},
            countyFloorPrices: {},
            loadingStatePrices: false,
            loadingCountyPrices: false,
            savingPrices: false,
            loweredFloorPolicyOptions: [
                { name: 'Lower prices for everyone', id: 1 },
                { name: 'Do not lower prices for current buyers', id: 2 },
            ],
            selectedLoweredFloorPolicy: 2,
            loweredFloorPolicyDescription: {
                1: 'No action will be taken. \nCampaigns with no explicit bid price set will immediately use the new floor price.',
                2: 'Campaigns which have recently purchased leads will have a bid price set at the pre-modified price. This means they will continue to receive leads at the existing price, unless they modify their bid.\nNew campaigns will default to the new floor price.'
            },
            priceMargin: {},
            loadingPriceMargin: false,
            statistics: {
                campaign_stats: {},
                legs_sold_stats: {}
            },
            loadingStatistics: false
        }
    },
    created() {
        this.initialize();
    },
    computed: {
        stateLoadingClass() {
            if (this.loadingAll && this.darkMode) {
                return 'text-white';
            }

            if (this.loadingAll && !this.darkMode) {
                return 'text-slate-500';
            }

            return 'text-primary-500 cursor-pointer';
        },
        suggestedStatePrices() {
            return this.suggestedPrices[this.suggestedPricesKey]?.find(price => price.location.type === 'st');
        },
        suggestedCountyPrices() {
            return this.suggestedPrices[this.suggestedPricesKey]?.filter(price => price.location.type === 'co');
        },
        suggestedPricesKey() {
            return `${this.industryId}.${this.selectedState.state_key}`;
        },
        selectedPrices() {
            return this.suggestedPrices[this.suggestedPricesKey]?.filter(price => !!price.selected) ?? [];
        },
        loadingAll() {
            return this.loading || this.loadingStatePrices || this.loadingCountyPrices;
        }
    },
    methods: {
        initialize() {
            this.industryFilterOptions = this.configuration.industries.map(industry => Object.assign({}, {id: industry.id, name: industry.name}));
            this.industryId = this.configuration.industries[0].id;
            this.industryServiceOptions = this.configuration.industries[0].services.map(service => Object.assign({}, {id: service.id, name: service.name}));
            this.industryServiceId = this.configuration.industries[0].services[0].id;
            this.propertyTypeFilterOptions = this.configuration.property_types.map(type => Object.assign({}, {id: type, name: type}))
            this.qualityTierFilterOptions = this.configuration.quality_tiers.map(tier => Object.assign({}, {id: tier, name: tier}));

            this.getStatePrices();
            this.getPriceMargin();
            this.getStatistics();
        },
        getPriceMargin() {
            this.loadingPriceMargin = true;

            this.api.getPriceMargin(this.industryId, this.industryServiceId)
                .then(resp => {
                    this.priceMargin = resp.data.data.pricing_margin;
                    this.getPriceSuggestions();
                }).catch(e => this.showAlert('error', e.response?.data?.message))
                .finally(() => this.loadingPriceMargin = false);
        },
        getPriceSuggestions() {
            if (!Object.keys(this.selectedState).length || this.suggestedPrices[this.suggestedPricesKey]) {
                return;
            }

            this.loading = true;
            this.api.getPriceSuggestions(this.selectedState.state_key, this.industryId, this.industryServiceId)
                .then(resp => {
                    this.suggestedPrices[this.suggestedPricesKey] = resp.data.data.suggested_prices;

                    this.suggestedPrices[this.suggestedPricesKey].forEach(price => price.selected = false);
                })
                .catch(e => this.showAlert('error', e.response?.data?.message))
                .finally(() => this.loading = false);
        },
        getStatePrices() {
            this.loadingStatePrices = true;
            this.stateFloorPrices = {};
            this.floorPriceApi.getData('Lead', this.industryId, this.industryServiceId, this.propertyType, this.qualityTier)
                .then(resp => this.stateFloorPrices = resp.data.data.prices)
                .catch(e => this.showAlert('error', e.response?.data?.message))
                .finally(() => this.loadingStatePrices = false);
        },
        getCountyPrices() {
            if (!Object.keys(this.selectedState).length) {
                return;
            }

            this.loadingCountyPrices = true;
            this.countyFloorPrices = {};
            this.floorPriceApi.getData('Lead', this.industryId, this.industryServiceId, this.propertyType, this.qualityTier, this.selectedState.id)
                .then(resp => this.countyFloorPrices = resp.data.data.prices)
                .catch(e => this.showAlert('error', e.response?.data?.message))
                .finally(() => this.loadingCountyPrices = false);
        },
        handleIndustryChange() {
            const services = this.configuration.industries.find(industry => industry.id === this.industryId).services;
            this.industryServiceOptions = services.map(service => Object.assign({}, {id: service.id, name: service.name}));

            if (services.length) {
                this.industryServiceId = services[0].id;
            } else {
                this.industryServiceId = null;
            }

            this.selectedState = {};
            this.resetPriceSuggestion();
            this.getStatePrices();
            this.getPriceMargin();
            this.getStatistics();
        },
        handleServiceChange() {
            this.selectedState = {};
            this.resetPriceSuggestion();
            this.getStatePrices();
            this.getPriceMargin();
            this.getStatistics();
        },
        setSelectedState(state) {
            if (this.loadingAll) return;

            this.selectedState = state;
            this.getPriceSuggestions();
            this.getCountyPrices();
            this.getStatistics();
        },
        getCurrentPrice(location, saleType) {
            let price = null;
            if (location.type === 'st') {
                price = this.stateFloorPrices[location.state_key];
            } else {
                price = this.countyFloorPrices[location.county_key];
            }

            return price ? (price[saleType] ? `$${price[saleType].explicit_price}` : 'Not set') : 'Not set';
        },
        applyPrices() {
            if (!this.selectedPrices.length) {
                return;
            }

            this.savingPrices = true;

            this.api.applySuggestedPrices(
                'Lead',
                this.industryId,
                this.industryServiceId,
                this.propertyType,
                this.qualityTier,
                this.selectedLoweredFloorPolicy,
                this.transformPricesForRequest())
                .then(() => {
                    this.getStatePrices();
                    this.getCountyPrices();
                    this.resetPriceSelection();
                })
                .catch(e => console.error(e))
                .finally(() => this.savingPrices = false);
        },
        transformPricesForRequest() {
            return this.selectedPrices.map(price => Object.assign({}, {
                location_id: price.location.id,
                exclusive: price.suggested_price_exclusive,
                duo: price.suggested_price_duo,
                trio: price.suggested_price_trio,
                quad: price.suggested_price_quad
            }));
        },
        resetPriceSelection() {
            this.suggestedPrices[this.suggestedPricesKey]?.forEach(price => price.selected = false);
        },
        updatePriceMargin() {
            this.loadingPriceMargin = true;

            this.api.updatePriceMargin(this.priceMargin.id, {
                exclusive: this.priceMargin.exclusive_margin,
                duo: this.priceMargin.duo_margin,
                trio: this.priceMargin.trio_margin,
                quad: this.priceMargin.quad_margin,
            })
                .then(() => {
                    this.resetPriceSuggestion();
                    this.getPriceSuggestions();
                })
                .catch(e => this.showAlert('error', e.response?.data?.message))
                .finally(() => this.loadingPriceMargin = false);
        },
        resetPriceSuggestion() {
            this.suggestedPrices = [];
        },
        getActiveCampaignsByKey(key) {
            return this.statistics.campaign_stats[key] ?? 0;
        },
        getLegsSoldByKey(key) {
            return this.statistics.legs_sold_stats[key] ?? 0;
        },
        getStatistics() {
            if (!Object.keys(this.selectedState).length) {
                return;
            }

            this.loadingStatistics = true;
            this.resetStatistics();

            this.api.getStatistics(this.industryId, this.industryServiceId, this.selectedState.state_key)
                .then(resp => {
                    this.statistics.campaign_stats = resp.data.data.campaign_stats ?? {};
                    this.statistics.legs_sold_stats = resp.data.data.legs_sold_stats ?? {};
                })
                .catch(e => this.showAlert('error', e.response?.data?.message))
                .finally(() => this.loadingStatistics = false);
        },

        resetStatistics() {
            this.statistics.campaign_stats = {};
            this.statistics.legs_sold_stats = {};
        }
    }
}
</script>
