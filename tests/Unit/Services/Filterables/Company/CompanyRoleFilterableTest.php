<?php

namespace Tests\Unit\Services\Filterables\Company;

use App\Models\User;
use App\Services\Filterables\Company\CompanyAccountManagerFilterable;
use App\Services\Filterables\Company\CompanyBusinessDevelopmentManagerFilterable;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CompanyRoleFilterableTest extends TestCase
{
    use RefreshDatabase;

    #[Test, DataProvider('roleProvider')]
    public function get_options($role, $filterable): void
    {
        $bob = User::factory()->withRole($role)->create(['name' => 'Bob']);
        $adam = User::factory()->withRole($role)->create(['name' => 'Adam']);

        $this->assertEquals([
            $adam->name => $adam->id,
            $bob->name => $bob->id,
        ], app($filterable)->getOptions());
    }

    public static function roleProvider()
    {
        return [
            'Account Manager' => [
                'account-manager',
                CompanyAccountManagerFilterable::class,
            ],
            'Business Development Manager' => [
                'business-development-manager',
                CompanyBusinessDevelopmentManagerFilterable::class,
            ],
        ];
    }
}
