<?php

namespace Tests\Feature\Repositories;

use App\Models\LeadProcessingCommunication;
use App\Models\LeadProcessor;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\User;
use App\Repositories\LeadProcessing\LeadCommunicationRepository;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Database\Seeders\LeadProcessingTimeframesSeeder;
use Database\Seeders\TimeframeContactBuffersSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class LeadCommunicationRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private readonly LeadCommunicationRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->repository = app(LeadCommunicationRepository::class);

        LeadProcessingCommunication::query()->delete();
    }

    #[Test]
    public function can_update_most_recent_communication_action(): void
    {
        $now = CarbonImmutable::now('UTC');

        LeadProcessingCommunication::factory(10)
            ->state(new Sequence(
                [LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID => 1],
                [LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID => 2]
            ))
            ->sequence(fn (Sequence $s) => [
                LeadProcessingCommunication::CREATED_AT => $now->addDays($s->index),
            ])
            ->create();

        $this->repository->updateMostRecentCommunication();

        $this->assertEquals(2, LeadProcessingCommunication::where(LeadProcessingCommunication::FIELD_MOST_RECENT, true)->count());

        LeadProcessingCommunication::query()
            ->selectRaw(sprintf(
                'MAX(%s) AS `created_at`, %s AS `consumer_product_id`',
                LeadProcessingCommunication::CREATED_AT,
                LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID
            ))
            ->groupBy('consumer_product_id')
            ->pluck('created_at', 'consumer_product_id')
            ->map(fn ($createdAt, $leadId) => $this->assertDatabaseHas(LeadProcessingCommunication::TABLE, [
                LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID => $leadId,
                LeadProcessingCommunication::FIELD_MOST_RECENT => 1,
                LeadProcessingCommunication::CREATED_AT => $createdAt,
            ]));
    }

    #[Test]
    public function can_update_most_recent_communication_action_with_lead_id(): void
    {
        $now = CarbonImmutable::now('UTC');

        LeadProcessingCommunication::factory(10)
            ->state(new Sequence(
                [LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID => 1],
                [LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID => 2]
            ))
            ->sequence(fn (Sequence $s) => [
                LeadProcessingCommunication::CREATED_AT => $now->addDays($s->index),
            ])
            ->createQuietly();

        $this->repository->updateMostRecentCommunication(2);

        $this->assertEquals(1, LeadProcessingCommunication::where(LeadProcessingCommunication::FIELD_MOST_RECENT, true)->count());

        $mostRecentCreatedAt = LeadProcessingCommunication::query()
            ->selectRaw(sprintf(
                'MAX(%s) AS `created_at`',
                LeadProcessingCommunication::CREATED_AT
            ))
            ->where(LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID, 2)
            ->groupBy(LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID)
            ->first()
            ->created_at;

        $this->assertDatabaseHas(LeadProcessingCommunication::TABLE, [
            LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID => 2,
            LeadProcessingCommunication::FIELD_MOST_RECENT => 1,
            LeadProcessingCommunication::CREATED_AT => $mostRecentCreatedAt,
        ]);

        $this->assertDatabaseMissing(LeadProcessingCommunication::TABLE, [
            LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID => 1,
            LeadProcessingCommunication::FIELD_MOST_RECENT => 1,
        ]);
    }

    #[Test]
    public function allow_calling_consumer_if_not_too_recently_contacted(): void
    {
        $this->seed([
            LeadProcessingTimeframesSeeder::class,
            TimeframeContactBuffersSeeder::class,
        ]);

        $user = User::factory()->create();

        $this->actingAs($user);

        $now = CarbonImmutable::now('UTC');

        CarbonImmutable::setTestNow($now);

        LeadProcessingCommunication::factory()->create([
            LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID => 1,
            LeadProcessingCommunication::FIELD_MOST_RECENT => true,
            LeadProcessingCommunication::FIELD_LEAD_PROCESSOR_ID => 0,
            LeadProcessingCommunication::CREATED_AT => $now->subHours(9),
        ]);

        $consumer = Consumer::factory()->create([
            Consumer::FIELD_LEGACY_ID => 1,
            Consumer::CREATED_AT => $now->subHours(10),
        ]);

        $this->assertTrue($this->repository->canCallConsumer($consumer));
    }

    #[Test]
    public function prevent_calling_consumer_if_too_recently_contacted(): void
    {
        $this->seed([
            LeadProcessingTimeframesSeeder::class,
            TimeframeContactBuffersSeeder::class,
        ]);

        $user = User::factory()->create();

        $this->actingAs($user);

        $now = CarbonImmutable::now('UTC');

        LeadProcessingCommunication::factory()->create([
            LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID => 1,
            LeadProcessingCommunication::FIELD_MOST_RECENT => true,
            LeadProcessingCommunication::FIELD_LEAD_PROCESSOR_ID => 0,
            LeadProcessingCommunication::CREATED_AT => $now,
        ]);

        $consumer = Consumer::factory()->create([
            Consumer::FIELD_ID        => 1,
            Consumer::CREATED_AT      => $now,
        ]);
        ConsumerProduct::factory()
            ->for($consumer)
            ->create([
                Consumer::FIELD_ID => 1,
            ]);


        $this->assertFalse($this->repository->canCallConsumer($consumer));
    }

    #[Test]
    public function can_call_consumer_if_user_is_lead_processor_and_different_processor_didnt_contact_recently(): void
    {
        $this->seed([
            LeadProcessingTimeframesSeeder::class,
            TimeframeContactBuffersSeeder::class,
        ]);

        $user = User::factory()->create();

        $this->actingAs($user);

        $processor = LeadProcessor::factory()->create([
            LeadProcessor::FIELD_USER_ID => $user->id,
        ]);

        $now = CarbonImmutable::now('UTC');

        CarbonImmutable::setTestNow($now);

        LeadProcessingCommunication::factory()->create([
            LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID => 1,
            LeadProcessingCommunication::FIELD_MOST_RECENT => true,
            LeadProcessingCommunication::FIELD_LEAD_PROCESSOR_ID => $processor->id + 1,
            LeadProcessingCommunication::CREATED_AT => $now->subHours(11),
        ]);

        $consumer = Consumer::factory()->create([
            Consumer::FIELD_ID        => 1,
            Consumer::CREATED_AT      => $now,
        ]);
        ConsumerProduct::factory()
            ->for($consumer)
            ->create([
                Consumer::FIELD_ID => 1,
            ]);


        $this->assertTrue($this->repository->canCallConsumer($consumer));
    }

    #[Test]
    public function cannot_call_consumer_if_user_is_lead_processor_and_different_processor_contacted_recently(): void
    {
        $this->seed([
            LeadProcessingTimeframesSeeder::class,
            TimeframeContactBuffersSeeder::class,
        ]);

        $user = User::factory()->create();

        $this->actingAs($user);

        $processor = LeadProcessor::factory()->create([
            LeadProcessor::FIELD_USER_ID => $user->id,
        ]);

        $now = CarbonImmutable::now('UTC');

        CarbonImmutable::setTestNow($now);

        LeadProcessingCommunication::factory()->create([
            LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID => 1,
            LeadProcessingCommunication::FIELD_MOST_RECENT => true,
            LeadProcessingCommunication::FIELD_LEAD_PROCESSOR_ID => $processor->id + 1,
            LeadProcessingCommunication::CREATED_AT => $now,
        ]);

        $consumer = Consumer::factory()->create([
            Consumer::FIELD_ID        => 1,
            Consumer::CREATED_AT      => $now,
        ]);
        ConsumerProduct::factory()
            ->for($consumer)
            ->create([
                Consumer::FIELD_ID => 1,
            ]);


        $this->assertFalse($this->repository->canCallConsumer($consumer));
    }

    #[Test]
    public function can_call_consumer_if_last_contacted_by_same_lead_processor(): void
    {
        $this->seed([
            LeadProcessingTimeframesSeeder::class,
            TimeframeContactBuffersSeeder::class,
        ]);

        $user = User::factory()->create();

        $this->actingAs($user);

        $processor = LeadProcessor::factory()->create([
            LeadProcessor::FIELD_USER_ID => $user->id,
        ]);

        $now = CarbonImmutable::now('UTC');

        $mostRecentCommunication = LeadProcessingCommunication::factory()->create([
            LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID => 1,
            LeadProcessingCommunication::FIELD_MOST_RECENT => true,
            LeadProcessingCommunication::FIELD_LEAD_PROCESSOR_ID => $processor->id + 1,
            LeadProcessingCommunication::CREATED_AT => $now,
        ]);

        $consumer = Consumer::factory()->create([
            Consumer::FIELD_ID        => 1,
            Consumer::CREATED_AT      => $now,
        ]);
        ConsumerProduct::factory()
            ->for($consumer)
            ->create([
                Consumer::FIELD_ID => 1,
            ]);


        $this->assertFalse($this->repository->canCallConsumer($consumer));

        $mostRecentCommunication->update([
            LeadProcessingCommunication::FIELD_LEAD_PROCESSOR_ID => $processor->id,
        ]);

        $this->assertTrue($this->repository->canCallConsumer($consumer->refresh()));
    }

    #[Test]
    public function can_call_consumer_if_user_is_lead_processor_and_no_one_recently_contacted(): void
    {
        $this->seed([
            LeadProcessingTimeframesSeeder::class,
            TimeframeContactBuffersSeeder::class,
        ]);

        $user = User::factory()->create();

        $this->actingAs($user);

        LeadProcessor::factory()->create([
            LeadProcessor::FIELD_USER_ID => $user->id,
        ]);

        $consumer = Consumer::factory()->create([
            Consumer::FIELD_ID        => 1,
        ]);
        ConsumerProduct::factory()
            ->for($consumer)
            ->create([
                Consumer::FIELD_ID => 1,
            ]);


        $this->assertTrue($this->repository->canCallConsumer($consumer));
    }

    #[Test]
    public function creating_communication_record_automatically_updates_most_recent_entry(): void
    {
        $this->assertDatabaseEmpty(LeadProcessingCommunication::TABLE);

        $now = Carbon::now('UTC');

        Carbon::setTestNow($now);

        LeadProcessingCommunication::create([
            LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID => 1,
            LeadProcessingCommunication::FIELD_TYPE => LeadProcessingCommunication::TYPE_SMS,
            LeadProcessingCommunication::FIELD_RELATION_ID => 1,
            LeadProcessingCommunication::FIELD_LEAD_PROCESSOR_ID => 1,
            LeadProcessingCommunication::FIELD_MOST_RECENT => false,
            Model::CREATED_AT => $now,
            Model::UPDATED_AT => $now,
        ]);

        $this->assertDatabaseHas(LeadProcessingCommunication::TABLE, [
            LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID => 1,
            LeadProcessingCommunication::FIELD_TYPE => LeadProcessingCommunication::TYPE_SMS,
            LeadProcessingCommunication::FIELD_RELATION_ID => 1,
            LeadProcessingCommunication::FIELD_LEAD_PROCESSOR_ID => 1,
            LeadProcessingCommunication::FIELD_MOST_RECENT => true,
            Model::CREATED_AT => $now,
            Model::UPDATED_AT => $now,
        ]);
    }
}
